/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./entrypoints/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "./hooks/**/*.{js,ts,jsx,tsx}",
    "./lib/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // 确保所有文本都能正确换行
      wordBreak: {
        'break-word': 'break-word',
        'break-all': 'break-all',
      },
      overflowWrap: {
        'anywhere': 'anywhere',
        'break-word': 'break-word',
      }
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
  ],
  // 添加自定义 CSS 变量
  corePlugins: {
    // 确保所有核心插件都启用
    wordBreak: true,
    overflowWrap: true,
  }
}
