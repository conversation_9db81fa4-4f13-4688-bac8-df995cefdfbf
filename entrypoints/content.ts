import TurndownService from 'turndown'
import { AIContentExtractor, collectDOMElementsInfo, generatePageStructure } from '@/lib/ai-content-extractor'
import { extractPageData } from '@/lib/content/extract'
import { handleContentMessages } from '@/lib/content/image-download'
import { findMatchingRule, extractContentByRule } from '@/lib/content/site-rules'
import { initSpaWatch } from '@/lib/content/spa-watch'

export default defineContentScript({
  matches: ['<all_urls>'], // 匹配所有网页
  main() {
    console.log('网页剪藏 Content Script 已加载');

    // 初始化HTML转Markdown服务
    const turndownService = new TurndownService({
      headingStyle: 'atx',
      codeBlockStyle: 'fenced',
      bulletListMarker: '-',
      emDelimiter: '_',
      strongDelimiter: '**',
      linkStyle: 'inlined'
    });

    // 添加自定义规则
    turndownService.addRule('strikethrough', {
      filter: ['del', 's'] as any,
      replacement: function (content) {
        return '~~' + content + '~~'
      }
    });

    turndownService.addRule('highlight', {
      filter: ['mark'],
      replacement: function (content) {
        return '==' + content + '=='
      }
    });

    // 添加 video 标签转换规则
    turndownService.addRule('video', {
      filter: 'video',
      replacement: function (content, node) {
        const videoEl = node as HTMLVideoElement;
        
        console.log('📹 开始处理 video 元素:', videoEl);
        console.log('📹 video.outerHTML:', videoEl.outerHTML);
        
        // HTML 实体解码函数
        function decodeHtmlEntities(str: string): string {
          const textarea = document.createElement('textarea');
          textarea.innerHTML = str;
          return textarea.value;
        }
        
        // 多种方式提取 video 源地址
        let src = '';
        
        // 方式1: 直接从 video.src 获取
        if (videoEl.src && videoEl.src.trim()) {
          src = videoEl.src.trim();
          console.log('📹 从 video.src 获取到视频源:', src);
        } 
        // 方式2: 从 source 子元素获取
        else {
          const sourceElements = videoEl.querySelectorAll('source');
          console.log('📹 找到 source 元素数量:', sourceElements.length);
          
          if (sourceElements.length > 0) {
            // 优先选择 mp4 格式，如果没有则选择第一个有效的
            let selectedSource: HTMLSourceElement | null = null;
            
            // 首先尝试找到 mp4 格式
            for (let i = 0; i < sourceElements.length; i++) {
              const sourceEl = sourceElements[i] as HTMLSourceElement;
              console.log(`📹 Source ${i + 1}: src="${sourceEl.src}", type="${sourceEl.type}"`);
              
              if (sourceEl.src && sourceEl.src.trim()) {
                if (!selectedSource) {
                  selectedSource = sourceEl; // 保底选择第一个有效的
                }
                
                if (sourceEl.type && sourceEl.type.includes('mp4')) {
                  selectedSource = sourceEl;
                  console.log(`📹 优先选择 mp4 格式的 source ${i + 1}`);
                  break;
                }
              }
            }
            
            if (selectedSource && selectedSource.src) {
              src = selectedSource.src.trim();
              console.log('📹 从 source 元素获取到视频源:', src);
            }
          }
        }
        
        // 方式3: 从原始 HTML 字符串中提取（备用方案，处理 HTML 实体编码）
        if (!src && videoEl.outerHTML) {
          const htmlStr = videoEl.outerHTML;
          console.log('📹 尝试从 outerHTML 提取:', htmlStr);
          
          // 先尝试匹配 source 标签中的 src
          const sourceMatches = htmlStr.match(/<source[^>]+src\s*=\s*["']([^"']+)["'][^>]*>/gi);
          if (sourceMatches) {
            console.log('📹 在 outerHTML 中找到 source 标签:', sourceMatches);
            
            for (const match of sourceMatches) {
              const srcMatch = match.match(/src\s*=\s*["']([^"']+)["']/i);
              if (srcMatch) {
                // 解码 HTML 实体
                src = decodeHtmlEntities(srcMatch[1].trim());
                console.log('📹 从 outerHTML source 提取到（解码后）:', src);
                
                // 如果是 mp4 格式，优先使用
                if (match.includes('mp4')) {
                  break;
                }
              }
            }
          }
          
          // 如果还没找到，尝试 video 标签的 src
          if (!src) {
            const videoSrcMatch = htmlStr.match(/<video[^>]+src\s*=\s*["']([^"']+)["']/i);
            if (videoSrcMatch) {
              // 解码 HTML 实体
              src = decodeHtmlEntities(videoSrcMatch[1].trim());
              console.log('📹 从 outerHTML video 提取到（解码后）:', src);
            }
          }
        }
        
        // 方式4: 如果前面都失败，尝试直接访问 DOM 属性（获取未编码的值）
        if (!src) {
          const sourceElements = videoEl.getElementsByTagName('source');
          console.log('📹 方式4: 使用 getElementsByTagName 查找 source，数量:', sourceElements.length);
          
          for (let i = 0; i < sourceElements.length; i++) {
            const sourceEl = sourceElements[i] as HTMLSourceElement;
            console.log(`📹 方式4 Source ${i + 1}: getAttribute('src')="${sourceEl.getAttribute('src')}", .src="${sourceEl.src}"`);
            
            // 尝试 getAttribute 获取原始值
            let sourceSrc = sourceEl.getAttribute('src') || sourceEl.src;
            if (sourceSrc && sourceSrc.trim()) {
              src = sourceSrc.trim();
              console.log('📹 方式4 获取到视频源:', src);
              break;
            }
          }
        }
        
        // 转换相对路径为绝对路径
        if (src && !src.startsWith('http') && !src.startsWith('data:')) {
          try {
            const baseUrl = window.location.origin + window.location.pathname;
            src = new URL(src, baseUrl).href;
            console.log('📹 转换相对路径为绝对路径:', src);
          } catch (error) {
            console.warn('📹 路径转换失败:', error);
          }
        }
        
        const poster = videoEl.poster || '';
        const controls = videoEl.hasAttribute('controls');
        const autoplay = videoEl.hasAttribute('autoplay') || videoEl.getAttribute('autoplay') === 'autoplay';
        const muted = videoEl.hasAttribute('muted') || videoEl.getAttribute('muted') === 'muted';
        const loop = videoEl.hasAttribute('loop') || videoEl.getAttribute('loop') === '';
        const playsinline = videoEl.hasAttribute('playsinline');
        
        // 提取视频标题（从父元素或相邻文本）
        let title = '视频内容';
        const parentText = videoEl.parentElement?.textContent?.trim();
        if (parentText && parentText.length < 100 && parentText !== content.trim()) {
          title = parentText;
        }
        
        console.log('📹 Video 转换信息:', { 
          src, 
          poster, 
          controls, 
          autoplay, 
          muted, 
          loop,
          playsinline,
          title,
          contentLength: content.length 
        });
        
        if (!src) {
          console.warn('⚠️ 未找到视频源，返回占位符');
          console.warn('⚠️ 完整的 video 元素:', videoEl.cloneNode(true));
          return content ? content : '`[VIDEO: 无可用视频源]`';
        }
        
        // 构建描述信息
        const attributes = [];
        if (controls) attributes.push('可控制');
        if (autoplay) attributes.push('自动播放');
        if (muted) attributes.push('静音');
        if (loop) attributes.push('循环');
        if (playsinline) attributes.push('内联播放');
        
        const attrDesc = attributes.length > 0 ? ` (${attributes.join(', ')})` : '';
        
        // 策略：转换为富信息块格式
        let result = `> 📹 **${title}**${attrDesc}\n> \n> 视频链接：[${src}](${src})`;
        
        if (poster) {
          result += `\n> 预览图：![视频预览](${poster})`;
        }
        
        if (content.trim()) {
          result += `\n> \n> ${content.trim()}`;
        }
        
        return '\n\n' + result + '\n\n';
      }
    });

    // 添加 iframe 标签转换规则（处理嵌入视频）
    turndownService.addRule('iframe', {
      filter: 'iframe',
      replacement: function (content, node) {
        const iframeEl = node as HTMLIFrameElement;
        const src = iframeEl.src || '';
        const title = iframeEl.title || '嵌入内容';
        const width = iframeEl.width || '';
        const height = iframeEl.height || '';
        
        if (!src) {
          return content || '`[IFRAME: 无可用源]`';
        }
        
        // 检测常见视频平台
        let platform = '嵌入内容';
        if (src.includes('youtube.com') || src.includes('youtu.be')) {
          platform = 'YouTube 视频';
        } else if (src.includes('vimeo.com')) {
          platform = 'Vimeo 视频';
        } else if (src.includes('bilibili.com')) {
          platform = 'B站视频';
        } else if (src.includes('douyin.com') || src.includes('tiktok.com')) {
          platform = '短视频';
        }
        
        const sizeInfo = width && height ? ` (${width}×${height})` : '';
        
        return `\n\n> 🎬 **${platform}**: ${title}${sizeInfo}\n> \n> 链接：[${src}](${src})\n\n`;
      }
    });

    /**
     * 网站特定的内容提取规则配置
     * 每个规则包含域名匹配模式和对应的选择器
     */
    const siteRules = [
      {
        // 少数派网站规则
        domains: ['sspai.com'],
        selectors: ['.article-body'],
        description: '少数派文章正文'
      },
      {
        // 小红书笔记规则
        domains: ['xiaohongshu.com'],
        selectors: [], // 使用特殊处理函数，不用通用选择器
        description: '小红书笔记',
        isXiaohongshu: true
      },
      {
        // 推特帖子规则
        domains: ['x.com', 'twitter.com'],
        selectors: [], // 使用特殊处理函数，不用通用选择器
        description: '推特帖子',
        isTwitter: true
      },
      // 可以继续添加其他网站的规则
      // {
      //   domains: ['zhihu.com'],
      //   selectors: ['.RichContent-inner', '.Post-RichTextContainer'],
      //   description: '知乎文章/回答正文'
      // },
    ];

    /**
     * 根据当前网站URL匹配对应的提取规则
     */
    // 本地 findMatchingRule / extractContentByRule 已迁移至 '@/lib/content/site-rules'


    /**
     * 推特帖子专用内容提取函数
     */
    function extractTwitterContent(): { html: string; markdown: string; title: string } | null {
      try {
        console.log('开始提取推特帖子内容');
        console.log('当前URL:', window.location.href);
        
        // 提取帖子ID
        const tweetId = extractTwitterPostId();
        console.log('推特帖子ID:', tweetId);
        
        // 提取article元素作为完整内容容器
        const articleElement = document.querySelector('article');
        if (!articleElement) {
          console.log('未找到推特article元素');
          return generateTwitterFallback(tweetId);
        }
        
        console.log('找到article元素:', articleElement);
        console.log('article元素的innerHTML长度:', articleElement.innerHTML.length);
        
        // 🔧 修复策略：先使用手动选择的成功方式，再增强清理
        // 直接对article元素应用cleanHTML，但使用增强的推特清理规则
        const cleanedHTML = cleanHTML(articleElement);
        console.log('清理后HTML长度:', cleanedHTML.length);
        console.log('清理后HTML预览:', cleanedHTML.substring(0, 200));
        
        // 检查是否有有效内容
        if (!cleanedHTML.trim() || cleanedHTML.trim().length < 10) {
          console.log('清理后的HTML内容为空，使用回退方案');
          return generateTwitterFallback(tweetId);
        }
        
        // 从清理后的内容中提取文本来生成标题
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = cleanedHTML;
        const textContent = tempDiv.textContent?.trim() || '';
        const title = textContent ? generateTwitterTitle(textContent) : `来自 x.com 的帖子_${tweetId}`;
        
        const finalHTML = `<div class="twitter-post">
${cleanedHTML}
</div>`;
        
        console.log('推特内容提取成功');
        console.log('- 标题:', title);
        console.log('- HTML长度:', finalHTML.length);
        console.log('- 有文本内容:', !!textContent);
        
        // 🔧 对推特内容应用特殊的Markdown转换，清理图片链接问题
        const markdown = convertTwitterContentToMarkdown(finalHTML);
        
        return {
          html: finalHTML,
          markdown: markdown,
          title: title
        };
        
      } catch (error) {
        console.error('推特内容提取失败:', error);
        const tweetId = extractTwitterPostId();
        return generateTwitterFallback(tweetId);
      }
    }
    
    /**
     * 推特内容专用的Markdown转换，处理图片链接问题
     */
    function convertTwitterContentToMarkdown(html: string): string {
      // 创建临时DOM来处理HTML
      const tempDiv = document.createElement('div');
      tempDiv.innerHTML = html;
      
      // 🔧 特殊处理：移除包装图片的用户链接，只保留图片本身
      const imageLinks = tempDiv.querySelectorAll('a[href^="/"]');
      imageLinks.forEach(link => {
        // 检查链接是否只包含图片（可能还有一些文本如"Click to Follow"）
        const images = link.querySelectorAll('img');
        const linkText = link.textContent?.trim() || '';
        
        if (images.length > 0) {
          // 如果链接包含图片，检查是否是用户头像/Follow链接
          const isUserLink = link.getAttribute('href')?.match(/^\/[^\/]+$/) && // 用户链接格式 /username
                            (linkText.includes('Follow') || linkText.includes('Click to') || 
                             images[0].src?.includes('profile_images')); // 头像图片
          
          if (isUserLink) {
            console.log('🗑️ 移除用户头像链接:', link.getAttribute('href'), linkText);
            // 移除整个链接（包括图片），因为这通常是用户头像
            link.remove();
          } else {
            // 保留图片，但移除链接包装
            images.forEach(img => {
              link.parentNode?.insertBefore(img.cloneNode(true), link);
            });
            link.remove();
          }
        } else if (linkText.includes('Follow') || linkText.includes('Click to')) {
          // 移除纯文本的Follow链接
          console.log('🗑️ 移除Follow文本链接:', linkText);
          link.remove();
        }
      });
      
      // 🔧 更精确地移除Follow相关文本，避免误删正文内容
      const followElements = tempDiv.querySelectorAll('a, span, div');
      followElements.forEach(el => {
        const text = el.textContent?.trim() || '';
        // 只删除明确的Follow相关文本，且不包含其他重要内容
        if ((text === 'Click to Follow' || 
             text.match(/^Click to Follow \w+$/) ||
             text.match(/^Follow \w+$/) ||
             text === '关注') && 
            text.length < 50) { // 确保不是包含Follow的长文本
          console.log('🗑️ 移除Follow文本:', text);
          el.remove();
        }
      });
      
      // 使用标准turndown转换处理后的HTML
      const markdown = turndownService.turndown(tempDiv.innerHTML);
      
      console.log('📝 推特Markdown转换完成，内容长度:', markdown.length);
      console.log('📝 Markdown预览:', markdown.substring(0, 100));
      
      // 🔧 安全检查：如果处理后内容为空或太短，回退到原始HTML的turndown
      if (!markdown.trim() || markdown.trim().length < 20) {
        console.log('⚠️ 处理后内容过短，回退到原始转换');
        return turndownService.turndown(html);
      }
      
      return markdown;
    }
    
    /**
     * 从URL中提取推特帖子ID
     */
    function extractTwitterPostId(): string {
      try {
        const url = window.location.href;
        // 推特URL格式: https://x.com/username/status/1234567890
        // 或: https://twitter.com/username/status/1234567890
        const match = url.match(/\/status\/(\d+)/);
        return match ? match[1] : 'unknown';
      } catch (error) {
        console.error('提取推特帖子ID失败:', error);
        return 'unknown';
      }
    }
    
    /**
     * 生成推特标题
     */
    function generateTwitterTitle(content: string): string {
      if (!content || content.trim().length === 0) {
        const tweetId = extractTwitterPostId();
        return `来自 x.com 的帖子_${tweetId}`;
      }
      
      // 移除多余的空白字符
      const cleanContent = content.replace(/\s+/g, ' ').trim();
      
      // 如果内容太短，直接使用
      if (cleanContent.length <= 50) {
        return cleanContent;
      }
      
      // 尝试使用第一行
      const firstLine = cleanContent.split('\n')[0]?.trim();
      if (firstLine && firstLine.length <= 50) {
        return firstLine;
      }
      
      // 尝试使用第一句（以句号、问号、感叹号结尾）
      const firstSentence = cleanContent.match(/^[^。！？.!?]*[。！？.!?]/)?.[0];
      if (firstSentence && firstSentence.length <= 50) {
        return firstSentence;
      }
      
      // 如果都太长，截取前50个字符
      return cleanContent.substring(0, 47) + '...';
    }
    
    /**
     * 生成推特回退标题和内容（用于纯图片/视频帖子）
     */
    function generateTwitterFallback(tweetId: string): { html: string; markdown: string; title: string } {
      const title = `来自 x.com 的帖子_${tweetId}`;
      const html = `<div class="twitter-post">
  <p>此推特帖子可能包含图片、视频或其他媒体内容。</p>
  <p>查看完整内容请访问: <a href="${window.location.href}">${window.location.href}</a></p>
</div>`;
      
      return {
        html: html,
        markdown: turndownService.turndown(html),
        title: title
      };
    }
    
    /**
     * 处理推特内容中的特殊元素
     */
    function processTwitterContent(content: string): string {
      if (!content) return '';
      
      // 转义HTML特殊字符
      return content
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        // 保持换行
        .replace(/\n/g, '<br>');
    }

    /**
     * 移除文本中的表情符号
     */
    function removeEmojis(text: string): string {
      if (!text) return '';
      
      // 移除 Unicode 表情符号
      // 包括各种表情符号范围
      const emojiRegex = /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}]|[\u{2000}-\u{206F}]|[\u{2E00}-\u{2E7F}]|[\u{3000}-\u{303F}]|[\u{2FF0}-\u{2FFF}]|[\u{3200}-\u{32FF}]|[\u{E000}-\u{F8FF}]|[\u{FE00}-\u{FE0F}]|[\u{FE30}-\u{FE4F}]|[\u{1F000}-\u{1F02F}]|[\u{1F0A0}-\u{1F0FF}]|[\u{1F100}-\u{1F64F}]|[\u{1F680}-\u{1F6FF}]|[\u{1F910}-\u{1F96B}]|[\u{1F980}-\u{1F997}]|[\u{1F9C0}-\u{1F9C2}]|[\u{1F9D0}-\u{1F9FF}]/gu;
      
      // 移除零宽度字符和其他特殊字符
      const zwjRegex = /[\u200D\u200C\uFEFF]/g;
      
      // 移除修饰符
      const modifierRegex = /[\u{1F3FB}-\u{1F3FF}]/gu;
      
      return text
        .replace(emojiRegex, '')
        .replace(zwjRegex, '')
        .replace(modifierRegex, '')
        .replace(/\s+/g, ' ') // 合并多个空格
        .trim();
    }

    /**
     * 使用AI辅助提取内容
     * 当AI配置启用时，优先使用AI分析来定位正文内容
     */
    async function extractContentWithAI(aiConfig: any): Promise<{ html: string; markdown: string; aiAnalysis?: any } | null> {
      try {
        console.log('🤖 开始AI辅助内容提取')
        
        // 收集页面DOM信息
        const domElements = collectDOMElementsInfo()
        const pageStructure = generatePageStructure()
        
        console.log('📊 收集到DOM元素信息:', domElements.length, '个候选元素')
        
        // 创建AI提取器
        const aiExtractor = new AIContentExtractor(aiConfig)
        
        // 请求AI分析
        const analysisResult = await aiExtractor.analyzePageStructure({
          url: window.location.href,
          title: document.title,
          domElements,
          pageStructure
        })
        
        console.log('🧠 AI分析结果:', analysisResult)
        
        if (!analysisResult.success) {
          console.warn('⚠️ AI分析失败:', analysisResult.error)
          return null
        }
        
        // 根据AI推荐的选择器提取内容
        for (const selector of analysisResult.selectors) {
          try {
            const element = document.querySelector(selector)
            if (element && element.textContent && element.textContent.trim().length > 100) {
              console.log(`✅ 使用AI推荐的选择器 "${selector}" 成功提取内容`)
              const cleanedHTML = cleanHTML(element)
              return {
                html: cleanedHTML,
                markdown: turndownService.turndown(cleanedHTML),
                aiAnalysis: {
                  selector: selector,
                  reasoning: analysisResult.reasoning,
                  confidence: analysisResult.confidence
                }
              }
            }
          } catch (error) {
            console.warn(`⚠️ 选择器 "${selector}" 提取失败:`, error)
            continue
          }
        }
        
        console.warn('⚠️ AI推荐的所有选择器都未能提取到有效内容')
        return null
        
      } catch (error) {
        console.error('❌ AI辅助提取失败:', error)
        return null
      }
    }

    /**
     * 智能提取网页正文内容（HTML格式）
     * 支持AI辅助提取，优先使用网站特定规则，回退到通用算法
     */
    async function extractMainContentAsHTML(aiConfig?: any): Promise<{ html: string; markdown: string; aiAnalysis?: any; coverImage?: string; title?: string }> {
      // 0. 如果启用了AI辅助，优先使用AI分析
      if (aiConfig && aiConfig.enabled && aiConfig.apiKey) {
        console.log('🤖 AI配置已启用，开始AI辅助内容提取')
        try {
          const aiResult = await extractContentWithAI(aiConfig)
          if (aiResult) {
            console.log('✅ AI辅助提取成功')
            return aiResult
          }
          console.log('⚠️ AI辅助提取失败，回退到传统算法')
        } catch (error) {
          console.error('❌ AI辅助提取异常:', error)
          console.log('🔄 回退到传统算法')
        }
      }

      // 1. 优先使用网站特定规则
      const matchingRule = findMatchingRule();
      if (matchingRule) {
        const ruleResult = extractContentByRule(matchingRule);
        if (ruleResult) {
          return ruleResult;
        }
        console.log('网站特定规则提取失败，回退到通用算法');
      }

      // 2. 使用通用语义化标签查找
      const semanticSelectors = [
        'article',
        'main',
        '[role="main"]',
        '.post-content',
        '.article-content',
        '.content',
        '.entry-content',
        '.post-body',
        '.article-body'
      ];

      for (const selector of semanticSelectors) {
        const element = document.querySelector(selector);
        if (element && element.textContent && element.textContent.trim().length > 200) {
          const cleanedHTML = cleanHTML(element);
          return {
            html: cleanedHTML,
            markdown: turndownService.turndown(cleanedHTML)
          };
        }
      }

      // 3. 分析所有段落，找到文本密度最高的区域
      const paragraphs = Array.from(document.querySelectorAll('p, div'));
      let maxScore = 0;
      let bestContainer: Element | null = null;

      for (const p of paragraphs) {
        const parent = p.parentElement;
        if (!parent) continue;

        // 计算文本密度分数
        const textLength = (parent.textContent || '').trim().length;
        const linkDensity = calculateLinkDensity(parent);
        const paragraphCount = parent.querySelectorAll('p').length;
        
        // 排除导航、侧边栏等区域
        const tagName = parent.tagName.toLowerCase();
        const className = parent.className.toLowerCase();
        const id = parent.id.toLowerCase();
        
        if (isNavigationElement(tagName, className, id)) {
          continue;
        }

        // 计算综合分数
        const score = textLength * (1 - linkDensity) * Math.log(paragraphCount + 1);
        
        if (score > maxScore && textLength > 100) {
          maxScore = score;
          bestContainer = parent;
        }
      }

      if (bestContainer) {
        const cleanedHTML = cleanHTML(bestContainer);
        return {
          html: cleanedHTML,
          markdown: turndownService.turndown(cleanedHTML)
        };
      }

      // 4. 兜底方案：获取body内的主要文本
      const bodyText = document.body.textContent || '';
      const plainText = cleanText(bodyText).substring(0, 2000) + '...';
      return {
        html: `<p>${plainText}</p>`,
        markdown: plainText
      };
    }

    /**
     * 清理HTML内容，移除不必要的元素和属性
     */
    function cleanHTML(element: Element): string {
      const clonedElement = element.cloneNode(true) as Element;
      
      // 检测是否为推特页面，使用专门的清理规则
      const isTwitter = window.location.hostname.includes('x.com') || window.location.hostname.includes('twitter.com');
      
      // 基础不需要的元素
      const basicUnwantedSelectors = [
        'script', 'style', 'nav', 'aside', 'footer', 'header',
        '.advertisement', '.ads', '.social-share', '.comments',
        '[class*="ad-"]', '[class*="ads-"]', '[id*="ad-"]',
        '.sidebar', '.related', '.recommend'
      ];
      
      // 推特专用的清理规则
      const twitterUnwantedSelectors = [
        // 交互按钮
        '[data-testid="reply"]',
        '[data-testid="retweet"]', 
        '[data-testid="like"]',
        '[data-testid="bookmark"]',
        '[data-testid="share"]',
        '[data-testid="unretweet"]',
        '[data-testid="unlike"]',
        '[data-testid="unBookmark"]',
        
        // 用户信息和头像（保留正文中的@提及）
        '[data-testid="UserAvatar"]',
        '[data-testid="User-Name"]',
        '[data-testid="UserScreenName"]',
        
        // 时间和来源信息
        '[data-testid="Time"]',
        '[data-testid="app-text-transition-container"]',
        
        // 广告和推荐
        '[data-testid="placementTracking"]',
        '[data-testid="promotedIndicator"]',
        
        // 导航和菜单
        '[role="button"]',
        '[aria-label*="菜单"]',
        '[aria-label*="更多"]',
        '[aria-label*="分享"]',
        '[aria-label*="Like"]',
        '[aria-label*="Reply"]',
        '[aria-label*="Repost"]',
        '[aria-label*="Bookmark"]',
        
        // 其他UI元素
        'button',
        '[role="menuitem"]',
        '[role="tab"]',
        '[role="tablist"]',
        '.r-*', // 推特的样式类通常以r-开头
        
        // 🔧 新增：用户头像和链接相关
        'a[href^="/"][href$=""]', // 用户链接
        'a[href*="/following"]',  // 关注链接
        'a[href*="/followers"]',  // 粉丝链接
        'a[aria-label*="Follow"]', // Follow 按钮链接
        'a[aria-label*="Click to Follow"]', // Click to Follow 链接
      ];
      
      const unwantedSelectors = isTwitter ? 
        [...basicUnwantedSelectors, ...twitterUnwantedSelectors] : 
        basicUnwantedSelectors;
      
      console.log(`🧹 清理HTML元素，使用${isTwitter ? '推特专用' : '通用'}规则，共${unwantedSelectors.length}个选择器`);
      
      unwantedSelectors.forEach(selector => {
        try {
          const elements = clonedElement.querySelectorAll(selector);
          if (elements.length > 0) {
            console.log(`🗑️ 移除 ${elements.length} 个 "${selector}" 元素`);
            elements.forEach(el => el.remove());
          }
        } catch (error) {
          // 某些选择器可能无效，忽略错误
          console.warn(`⚠️ 选择器 "${selector}" 无效:`, error);
        }
      });

      // 清理所有元素的属性，只保留必要的
      const allElements = clonedElement.querySelectorAll('*');
      allElements.forEach(el => {
        // 保留的属性
        const keepAttrs = [
          'href', 'src', 'alt', 'title',
          // Video 和 Audio 相关属性
          'controls', 'autoplay', 'muted', 'loop', 'preload', 'poster',
          // 尺寸属性
          'width', 'height',
          // Iframe 相关属性
          'frameborder', 'allowfullscreen',
          // 类型属性 - 对 source 元素很重要
          'type',
          // 媒体查询属性
          'media'
        ];
        const attrs = Array.from(el.attributes);
        
        attrs.forEach(attr => {
          if (!keepAttrs.includes(attr.name)) {
            el.removeAttribute(attr.name);
          }
        });

        // 处理图片：添加alt属性如果缺失
        if (el.tagName === 'IMG' && !el.getAttribute('alt')) {
          el.setAttribute('alt', '图片');
        }
      });

      const result = clonedElement.innerHTML;
      console.log(`✅ HTML清理完成，清理前: ${element.innerHTML.length} 字符，清理后: ${result.length} 字符`);
      return result;
    }

    /**
     * 计算链接密度（链接文本占总文本的比例）
     */
    function calculateLinkDensity(element: Element): number {
      const totalText = element.textContent?.length || 0;
      const linkText = Array.from(element.querySelectorAll('a'))
        .reduce((sum, link) => sum + (link.textContent?.length || 0), 0);
      
      return totalText > 0 ? linkText / totalText : 0;
    }

    /**
     * 判断是否为导航、广告等无关元素
     */
    function isNavigationElement(tagName: string, className: string, id: string): boolean {
      const navKeywords = [
        'nav', 'navigation', 'sidebar', 'footer', 'header', 'menu',
        'advertisement', 'ads', 'banner', 'popup', 'comment', 'social',
        'share', 'related', 'recommend', 'tag', 'category', 'breadcrumb',
        'pagination', 'widget', 'toolbar'
      ];

      const text = `${tagName} ${className} ${id}`.toLowerCase();
      return navKeywords.some(keyword => text.includes(keyword));
    }

    /**
     * 清理文本内容
     */
    function cleanText(text: string): string {
      return text
        .replace(/\s+/g, ' ') // 合并多个空白字符
        .replace(/^\s+|\s+$/g, '') // 去除首尾空格
        .replace(/\n\s*\n/g, '\n') // 去除多余换行
        .trim();
    }

    /**
     * 获取网页站点信息
     */
    function getSiteInfo(): string {
      const hostname = window.location.hostname;
      const siteName = document.querySelector('meta[property="og:site_name"]')?.getAttribute('content');
      return siteName || hostname;
    }

    /**
     * 获取网站图标
     */
    function getFavicon(): string {
      const favicon = document.querySelector('link[rel*="icon"]') as HTMLLinkElement;
      if (favicon && favicon.href) {
        return favicon.href;
      }
      return `${window.location.origin}/favicon.ico`;
    }

    /**
     * 提取封面图片
     * 支持 meta 标签、文章首图检测等多种方式
     */
    function extractCoverImage(): string | null {
      // 1. 优先使用 Open Graph 图片
      const ogImage = document.querySelector('meta[property="og:image"]')?.getAttribute('content');
      if (ogImage && isValidImageUrl(ogImage)) {
        console.log('使用 og:image 作为封面图片:', ogImage);
        return resolveImageUrl(ogImage);
      }

      // 2. 使用 Twitter Card 图片
      const twitterImage = document.querySelector('meta[name="twitter:image"]')?.getAttribute('content');
      if (twitterImage && isValidImageUrl(twitterImage)) {
        console.log('使用 twitter:image 作为封面图片:', twitterImage);
        return resolveImageUrl(twitterImage);
      }

      // 3. 查找文章首图
      const firstArticleImage = findFirstArticleImage();
      if (firstArticleImage) {
        console.log('使用文章首图作为封面图片:', firstArticleImage);
        return firstArticleImage;
      }

      // 4. 其他 meta 标签图片
      const metaImageSelectors = [
        'meta[name="twitter:image:src"]',
        'meta[property="og:image:url"]',
        'meta[name="image"]',
        'meta[property="image"]'
      ];

      for (const selector of metaImageSelectors) {
        const element = document.querySelector(selector);
        const imageUrl = element?.getAttribute('content');
        if (imageUrl && isValidImageUrl(imageUrl)) {
          console.log(`使用 ${selector} 作为封面图片:`, imageUrl);
          return resolveImageUrl(imageUrl);
        }
      }

      console.log('未找到合适的封面图片');
      return null;
    }

    /**
     * 查找文章首图
     */
    function findFirstArticleImage(): string | null {
      // 定义搜索范围：文章内容区域
      const contentSelectors = [
        'article img',
        'main img',
        '[role="main"] img',
        '.post-content img',
        '.article-content img',
        '.content img',
        '.entry-content img',
        '.post-body img',
        '.article-body img'
      ];

      for (const selector of contentSelectors) {
        const images = document.querySelectorAll(selector) as NodeListOf<HTMLImageElement>;
        
        for (const img of images) {
          const src = img.src || img.getAttribute('data-src');
          if (src && isValidImageUrl(src) && isValidImageSize(img)) {
            return resolveImageUrl(src);
          }
        }
      }

      // 如果内容区域没有找到，搜索整个页面的第一张有效图片
      const allImages = document.querySelectorAll('img') as NodeListOf<HTMLImageElement>;
      for (const img of allImages) {
        const src = img.src || img.getAttribute('data-src');
        if (src && isValidImageUrl(src) && isValidImageSize(img)) {
          return resolveImageUrl(src);
        }
      }

      return null;
    }

    /**
     * 验证图片URL是否有效
     */
    function isValidImageUrl(url: string): boolean {
      if (!url || url.length === 0) return false;
      
      // 排除一些明显不是内容图片的URL
      const excludePatterns = [
        /favicon/i,
        /logo/i,
        /icon/i,
        /avatar/i,
        /profile/i,
        /thumbnail/i,
        /ads?[_-]/i,
        /advertisement/i,
        /banner/i,
        /widget/i,
        /button/i,
        /social/i,
        /share/i,
        /tracking/i,
        /analytics/i,
        /placeholder/i,
        /default/i,
        /no-image/i,
        /\.gif$/i // 排除 GIF 图片（通常是动画或装饰性图片）
      ];

      return !excludePatterns.some(pattern => pattern.test(url));
    }

    /**
     * 验证图片尺寸是否合适作为封面图片
     */
    function isValidImageSize(img: HTMLImageElement): boolean {
      const width = img.naturalWidth || img.width;
      const height = img.naturalHeight || img.height;
      
      // 图片尺寸过小的不适合作为封面
      if (width < 100 || height < 100) return false;
      
      // 图片比例过于极端的不适合作为封面
      const aspectRatio = width / height;
      if (aspectRatio > 5 || aspectRatio < 0.2) return false;
      
      return true;
    }

    /**
     * 解析图片URL为绝对URL
     */
    function resolveImageUrl(url: string): string {
      try {
        // 如果已经是绝对URL，直接返回
        if (url.startsWith('http://') || url.startsWith('https://')) {
          return url;
        }
        
        // 如果是相对URL，转换为绝对URL
        if (url.startsWith('//')) {
          return `${window.location.protocol}${url}`;
        }
        
        if (url.startsWith('/')) {
          return `${window.location.origin}${url}`;
        }
        
        // 相对于当前路径的URL
        const base = window.location.href.substring(0, window.location.href.lastIndexOf('/') + 1);
        return new URL(url, base).href;
      } catch (error) {
        console.warn('图片URL解析失败:', url, error);
        return url;
      }
    }

    /**
     * 提取作者信息
     * 支持 meta 标签、常见网站的作者区域检测和 JSON-LD 结构化数据
     */
    function extractAuthorInfo(): string | null {
      // 1. 优先使用 meta 标签
      const metaAuthorSelectors = [
        'meta[name="author"]',
        'meta[property="author"]',
        'meta[name="article:author"]',
        'meta[property="article:author"]',
        'meta[name="creator"]',
        'meta[property="creator"]',
        'meta[name="DC.creator"]',
        'meta[property="DC.creator"]'
      ];

      for (const selector of metaAuthorSelectors) {
        const element = document.querySelector(selector);
        const author = element?.getAttribute('content');
        if (author && author.trim()) {
          console.log(`使用 ${selector} 获取作者信息:`, author);
          return author.trim();
        }
      }

      // 2. 尝试从 JSON-LD 结构化数据中提取
      const jsonLdAuthor = extractAuthorFromJsonLd();
      if (jsonLdAuthor) {
        console.log('使用 JSON-LD 获取作者信息:', jsonLdAuthor);
        return jsonLdAuthor;
      }

      // 3. 使用常见网站的作者区域检测
      const siteAuthor = extractAuthorFromSitePatterns();
      if (siteAuthor) {
        console.log('使用网站模式获取作者信息:', siteAuthor);
        return siteAuthor;
      }

      // 4. 使用通用选择器查找作者信息
      const genericAuthor = extractAuthorFromGenericSelectors();
      if (genericAuthor) {
        console.log('使用通用选择器获取作者信息:', genericAuthor);
        return genericAuthor;
      }

      console.log('未找到作者信息');
      return null;
    }

    /**
     * 从 JSON-LD 结构化数据中提取作者信息
     */
    function extractAuthorFromJsonLd(): string | null {
      const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
      
      for (const script of jsonLdScripts) {
        try {
          const data = JSON.parse(script.textContent || '');
          const author = extractAuthorFromJsonLdData(data);
          if (author) return author;
        } catch (error) {
          console.warn('解析 JSON-LD 数据失败:', error);
        }
      }
      
      return null;
    }

    /**
     * 递归提取 JSON-LD 数据中的作者信息
     */
    function extractAuthorFromJsonLdData(data: any): string | null {
      if (!data || typeof data !== 'object') return null;

      // 处理数组
      if (Array.isArray(data)) {
        for (const item of data) {
          const author = extractAuthorFromJsonLdData(item);
          if (author) return author;
        }
        return null;
      }

      // 直接的作者字段
      if (data.author) {
        if (typeof data.author === 'string') {
          return data.author;
        }
        if (typeof data.author === 'object') {
          return data.author.name || data.author['@name'] || null;
        }
      }

      // 创建者字段
      if (data.creator) {
        if (typeof data.creator === 'string') {
          return data.creator;
        }
        if (typeof data.creator === 'object') {
          return data.creator.name || data.creator['@name'] || null;
        }
      }

      // 特定类型的文章作者
      if (data['@type'] === 'Article' || data['@type'] === 'BlogPosting' || data['@type'] === 'NewsArticle') {
        if (data.author) {
          if (typeof data.author === 'string') return data.author;
          if (data.author.name) return data.author.name;
          if (Array.isArray(data.author) && data.author[0]) {
            return data.author[0].name || data.author[0];
          }
        }
      }

      // 递归搜索嵌套对象
      for (const key in data) {
        if (typeof data[key] === 'object') {
          const author = extractAuthorFromJsonLdData(data[key]);
          if (author) return author;
        }
      }

      return null;
    }

    /**
     * 使用网站特定的作者区域检测
     */
    function extractAuthorFromSitePatterns(): string | null {
      const hostname = window.location.hostname;
      
      // 网站特定的作者选择器
      const sitePatterns = [
        {
          // 知乎
          domains: ['zhihu.com'],
          selectors: [
            '.AuthorInfo-name',
            '.UserLink-link',
            '.author-link-line .author-link',
            '.UserLink .UserLink-link'
          ]
        },
        {
          // 微信公众号
          domains: ['mp.weixin.qq.com'],
          selectors: [
            '.rich_media_meta_text',
            '.rich_media_meta_link',
            '#js_name',
            '.account_nickname'
          ]
        },
        {
          // 掘金
          domains: ['juejin.cn', 'juejin.im'],
          selectors: [
            '.username',
            '.author-name',
            '.user-name',
            '.entry-author .author-name'
          ]
        },
        {
          // 少数派
          domains: ['sspai.com'],
          selectors: [
            '.article-author .author-name',
            '.author-info .author-name',
            '.author-link'
          ]
        },
        {
          // Medium
          domains: ['medium.com'],
          selectors: [
            '[data-testid="authorName"]',
            '.author-name',
            '.ui-caption.js-authorName'
          ]
        },
        {
          // 简书
          domains: ['jianshu.com'],
          selectors: [
            '.author .name',
            '.author-name',
            '.follow-detail .name'
          ]
        },
        {
          // CSDN
          domains: ['csdn.net'],
          selectors: [
            '.follow-nickName',
            '.user-info .name',
            '.author-info .name'
          ]
        },
        {
          // 博客园
          domains: ['cnblogs.com'],
          selectors: [
            '.postTitle2 a',
            '.author',
            '.blogStats .author'
          ]
        }
      ];

      for (const pattern of sitePatterns) {
        for (const domain of pattern.domains) {
          if (hostname.includes(domain)) {
            for (const selector of pattern.selectors) {
              const element = document.querySelector(selector);
              if (element) {
                const author = element.textContent?.trim();
                if (author && author.length > 0 && author.length < 100) {
                  return author;
                }
              }
            }
          }
        }
      }

      return null;
    }

    /**
     * 使用通用选择器查找作者信息
     */
    function extractAuthorFromGenericSelectors(): string | null {
      const genericSelectors = [
        // 通用作者选择器
        '.author',
        '.author-name',
        '.author-info',
        '.post-author',
        '.article-author',
        '.entry-author',
        '.byline',
        '.writer',
        '.creator',
        
        // 带有作者关键词的选择器
        '[class*="author"]',
        '[class*="byline"]',
        '[class*="writer"]',
        '[id*="author"]',
        
        // 微数据格式
        '[itemprop="author"]',
        '[itemprop="creator"]',
        
        // 语义化标签
        'address',
        '.vcard .fn',
        '.h-card .p-name'
      ];

      for (const selector of genericSelectors) {
        const elements = document.querySelectorAll(selector);
        
        for (const element of elements) {
          const author = element.textContent?.trim();
          if (author && isValidAuthorName(author)) {
            return author;
          }
        }
      }

      return null;
    }

    /**
     * 提取发布日期
     * 支持 meta 标签、time 元素检测和 JSON-LD 结构化数据
     */
    function extractPublishDate(): string | null {
      // 1. 优先使用 meta 标签
      const metaDateSelectors = [
        'meta[name="article:published_time"]',
        'meta[property="article:published_time"]',
        'meta[name="publishedDate"]',
        'meta[property="publishedDate"]',
        'meta[name="publication_date"]',
        'meta[property="publication_date"]',
        'meta[name="date"]',
        'meta[property="date"]',
        'meta[name="DC.date"]',
        'meta[property="DC.date"]',
        'meta[name="DC.date.created"]',
        'meta[property="DC.date.created"]',
        'meta[name="created"]',
        'meta[property="created"]',
        'meta[name="datePublished"]',
        'meta[property="datePublished"]'
      ];

      for (const selector of metaDateSelectors) {
        const element = document.querySelector(selector);
        const dateStr = element?.getAttribute('content');
        if (dateStr) {
          const parsedDate = parseDate(dateStr);
          if (parsedDate) {
            console.log(`使用 ${selector} 获取发布日期:`, parsedDate);
            return parsedDate;
          }
        }
      }

      // 2. 尝试从 JSON-LD 结构化数据中提取
      const jsonLdDate = extractDateFromJsonLd();
      if (jsonLdDate) {
        console.log('使用 JSON-LD 获取发布日期:', jsonLdDate);
        return jsonLdDate;
      }

      // 3. 使用 time 元素检测
      const timeDate = extractDateFromTimeElements();
      if (timeDate) {
        console.log('使用 time 元素获取发布日期:', timeDate);
        return timeDate;
      }

      // 4. 使用网站特定的日期区域检测
      const siteDate = extractDateFromSitePatterns();
      if (siteDate) {
        console.log('使用网站模式获取发布日期:', siteDate);
        return siteDate;
      }

      // 5. 使用通用选择器查找日期信息
      const genericDate = extractDateFromGenericSelectors();
      if (genericDate) {
        console.log('使用通用选择器获取发布日期:', genericDate);
        return genericDate;
      }

      console.log('未找到发布日期信息');
      return null;
    }

    /**
     * 从 JSON-LD 结构化数据中提取日期信息
     */
    function extractDateFromJsonLd(): string | null {
      const jsonLdScripts = document.querySelectorAll('script[type="application/ld+json"]');
      
      for (const script of jsonLdScripts) {
        try {
          const data = JSON.parse(script.textContent || '');
          const date = extractDateFromJsonLdData(data);
          if (date) return date;
        } catch (error) {
          console.warn('解析 JSON-LD 数据失败:', error);
        }
      }
      
      return null;
    }

    /**
     * 递归提取 JSON-LD 数据中的日期信息
     */
    function extractDateFromJsonLdData(data: any): string | null {
      if (!data || typeof data !== 'object') return null;

      // 处理数组
      if (Array.isArray(data)) {
        for (const item of data) {
          const date = extractDateFromJsonLdData(item);
          if (date) return date;
        }
        return null;
      }

      // 日期相关字段
      const dateFields = [
        'datePublished',
        'dateCreated',
        'publishedDate',
        'createdDate',
        'publicationDate',
        'dateModified',
        'modifiedDate',
        'uploadDate',
        'datePosted'
      ];

      for (const field of dateFields) {
        if (data[field]) {
          const parsedDate = parseDate(data[field]);
          if (parsedDate) return parsedDate;
        }
      }

      // 特定类型的文章日期
      if (data['@type'] === 'Article' || data['@type'] === 'BlogPosting' || data['@type'] === 'NewsArticle') {
        for (const field of dateFields) {
          if (data[field]) {
            const parsedDate = parseDate(data[field]);
            if (parsedDate) return parsedDate;
          }
        }
      }

      // 递归搜索嵌套对象
      for (const key in data) {
        if (typeof data[key] === 'object') {
          const date = extractDateFromJsonLdData(data[key]);
          if (date) return date;
        }
      }

      return null;
    }

    /**
     * 从 time 元素中提取日期
     */
    function extractDateFromTimeElements(): string | null {
      const timeElements = document.querySelectorAll('time');
      
      for (const timeElement of timeElements) {
        // 优先使用 datetime 属性
        const datetime = timeElement.getAttribute('datetime');
        if (datetime) {
          const parsedDate = parseDate(datetime);
          if (parsedDate) return parsedDate;
        }
        
        // 如果没有 datetime 属性，尝试解析文本内容
        const textContent = timeElement.textContent?.trim();
        if (textContent) {
          const parsedDate = parseDate(textContent);
          if (parsedDate) return parsedDate;
        }
      }
      
      return null;
    }

    /**
     * 使用网站特定的日期区域检测
     */
    function extractDateFromSitePatterns(): string | null {
      const hostname = window.location.hostname;
      
      // 网站特定的日期选择器
      const sitePatterns = [
        {
          // 知乎
          domains: ['zhihu.com'],
          selectors: [
            '.ContentItem-time',
            '.Question-mainColumnTimeText',
            '.ContentItem-meta .Popover-content time',
            '.Question-mainColumn .ContentItem-time'
          ]
        },
        {
          // 微信公众号
          domains: ['mp.weixin.qq.com'],
          selectors: [
            '.rich_media_meta_text',
            '.rich_media_meta_list em',
            '#publish_time',
            '.rich_media_meta .rich_media_meta_text'
          ]
        },
        {
          // 掘金
          domains: ['juejin.cn', 'juejin.im'],
          selectors: [
            '.meta-box time',
            '.article-info time',
            '.entry-meta time',
            '.meta-container time'
          ]
        },
        {
          // 少数派
          domains: ['sspai.com'],
          selectors: [
            '.article-meta time',
            '.article-info .time',
            '.post-meta time',
            '.entry-meta .time'
          ]
        },
        {
          // Medium
          domains: ['medium.com'],
          selectors: [
            '[data-testid="storyPublishDate"]',
            '.published-date',
            'time[datetime]'
          ]
        },
        {
          // 简书
          domains: ['jianshu.com'],
          selectors: [
            '.publish-time',
            '.meta-bottom time',
            '.author .time'
          ]
        },
        {
          // CSDN
          domains: ['csdn.net'],
          selectors: [
            '.article-info-box .time',
            '.article-bar-top .time',
            '.blog-info-box .time'
          ]
        },
        {
          // 博客园
          domains: ['cnblogs.com'],
          selectors: [
            '.postTitle2',
            '.postDesc time',
            '.post-date'
          ]
        }
      ];

      for (const pattern of sitePatterns) {
        for (const domain of pattern.domains) {
          if (hostname.includes(domain)) {
            for (const selector of pattern.selectors) {
              const element = document.querySelector(selector);
              if (element) {
                const dateText = element.textContent?.trim() || element.getAttribute('datetime');
                if (dateText) {
                  const parsedDate = parseDate(dateText);
                  if (parsedDate) return parsedDate;
                }
              }
            }
          }
        }
      }

      return null;
    }

    /**
     * 使用通用选择器查找日期信息
     */
    function extractDateFromGenericSelectors(): string | null {
      const genericSelectors = [
        // 通用日期选择器
        '.date',
        '.publish-date',
        '.published-date',
        '.publication-date',
        '.post-date',
        '.article-date',
        '.entry-date',
        '.created-date',
        '.time',
        '.timestamp',
        '.publish-time',
        '.post-time',
        '.article-time',
        '.entry-time',
        '.meta-date',
        '.meta-time',
        
        // 带有日期关键词的选择器
        '[class*="date"]',
        '[class*="time"]',
        '[class*="publish"]',
        '[id*="date"]',
        '[id*="time"]',
        '[id*="publish"]',
        
        // 微数据格式
        '[itemprop="datePublished"]',
        '[itemprop="dateCreated"]',
        '[itemprop="publishDate"]',
        '[itemprop="uploadDate"]',
        '[itemprop="datePosted"]',
        
        // 通用时间元素
        'time',
        '.byline time',
        '.author-date',
        '.post-meta time',
        '.article-meta time',
        '.entry-meta time'
      ];

      for (const selector of genericSelectors) {
        const elements = document.querySelectorAll(selector);
        
        for (const element of elements) {
          const dateText = element.textContent?.trim() || element.getAttribute('datetime');
          if (dateText) {
            const parsedDate = parseDate(dateText);
            if (parsedDate && isValidPublishDate(parsedDate)) {
              return parsedDate;
            }
          }
        }
      }

      return null;
    }

    /**
     * 解析日期字符串为标准格式
     */
    function parseDate(dateString: string): string | null {
      if (!dateString || typeof dateString !== 'string') return null;
      
      try {
        // 清理日期字符串
        let cleanedDate = dateString.trim();
        
        // 移除常见的中文日期前缀
        cleanedDate = cleanedDate.replace(/^(发布时间|发布日期|创建时间|创建日期|更新时间|更新日期|时间|日期|发表于|发布于)[:：\s]*/, '');
        
        // 移除常见的英文日期前缀
        cleanedDate = cleanedDate.replace(/^(Published|Created|Updated|Posted|Date|Time)[:：\s]*/, '');
        
        // 处理相对时间（如"2天前"、"1小时前"等）
        const relativeTimeResult = parseRelativeTime(cleanedDate);
        if (relativeTimeResult) return relativeTimeResult;
        
        // 尝试使用 Date 构造函数解析
        const date = new Date(cleanedDate);
        if (isValidDate(date)) {
          return formatDate(date);
        }
        
        // 尝试常见的中文日期格式
        const chineseDateResult = parseChineseDate(cleanedDate);
        if (chineseDateResult) return chineseDateResult;
        
        // 尝试其他常见格式
        const otherFormatResult = parseOtherFormats(cleanedDate);
        if (otherFormatResult) return otherFormatResult;
        
      } catch (error) {
        console.warn('日期解析失败:', dateString, error);
      }
      
      return null;
    }

    /**
     * 解析相对时间（如"2天前"、"1小时前"等）
     */
    function parseRelativeTime(dateString: string): string | null {
      const now = new Date();
      
      // 中文相对时间
      const chinesePatterns = [
        { pattern: /(\d+)分钟前/, unit: 'minutes' },
        { pattern: /(\d+)小时前/, unit: 'hours' },
        { pattern: /(\d+)天前/, unit: 'days' },
        { pattern: /(\d+)周前/, unit: 'weeks' },
        { pattern: /(\d+)个月前/, unit: 'months' },
        { pattern: /(\d+)年前/, unit: 'years' },
        { pattern: /昨天/, unit: 'days', value: 1 },
        { pattern: /前天/, unit: 'days', value: 2 },
        { pattern: /上周/, unit: 'weeks', value: 1 },
        { pattern: /上个月/, unit: 'months', value: 1 },
        { pattern: /去年/, unit: 'years', value: 1 }
      ];
      
      // 英文相对时间
      const englishPatterns = [
        { pattern: /(\d+)\s*minutes?\s*ago/, unit: 'minutes' },
        { pattern: /(\d+)\s*hours?\s*ago/, unit: 'hours' },
        { pattern: /(\d+)\s*days?\s*ago/, unit: 'days' },
        { pattern: /(\d+)\s*weeks?\s*ago/, unit: 'weeks' },
        { pattern: /(\d+)\s*months?\s*ago/, unit: 'months' },
        { pattern: /(\d+)\s*years?\s*ago/, unit: 'years' },
        { pattern: /yesterday/i, unit: 'days', value: 1 },
        { pattern: /last\s*week/i, unit: 'weeks', value: 1 },
        { pattern: /last\s*month/i, unit: 'months', value: 1 },
        { pattern: /last\s*year/i, unit: 'years', value: 1 }
      ];
      
      const allPatterns = [...chinesePatterns, ...englishPatterns];
      
      for (const { pattern, unit, value } of allPatterns) {
        const match = dateString.match(pattern);
        if (match) {
          const amount = value || parseInt(match[1]);
          if (amount) {
            const date = new Date(now);
            switch (unit) {
              case 'minutes':
                date.setMinutes(date.getMinutes() - amount);
                break;
              case 'hours':
                date.setHours(date.getHours() - amount);
                break;
              case 'days':
                date.setDate(date.getDate() - amount);
                break;
              case 'weeks':
                date.setDate(date.getDate() - amount * 7);
                break;
              case 'months':
                date.setMonth(date.getMonth() - amount);
                break;
              case 'years':
                date.setFullYear(date.getFullYear() - amount);
                break;
            }
            return formatDate(date);
          }
        }
      }
      
      return null;
    }

    /**
     * 解析中文日期格式
     */
    function parseChineseDate(dateString: string): string | null {
      const patterns = [
        // 2024年1月10日
        /(\d{4})年(\d{1,2})月(\d{1,2})日/,
        // 2024-01-10
        /(\d{4})-(\d{1,2})-(\d{1,2})/,
        // 2024/01/10
        /(\d{4})\/(\d{1,2})\/(\d{1,2})/,
        // 2024.01.10
        /(\d{4})\.(\d{1,2})\.(\d{1,2})/,
        // 01-10 (当年)
        /(\d{1,2})-(\d{1,2})$/,
        // 01/10 (当年)
        /(\d{1,2})\/(\d{1,2})$/
      ];
      
      for (const pattern of patterns) {
        const match = dateString.match(pattern);
        if (match) {
          let year, month, day;
          
          if (match.length === 4) {
            // 完整日期
            year = parseInt(match[1]);
            month = parseInt(match[2]);
            day = parseInt(match[3]);
          } else if (match.length === 3) {
            // 只有月日，使用当前年份
            year = new Date().getFullYear();
            month = parseInt(match[1]);
            day = parseInt(match[2]);
          } else {
            continue;
          }
          
          const date = new Date(year, month - 1, day);
          if (isValidDate(date)) {
            return formatDate(date);
          }
        }
      }
      
      return null;
    }

    /**
     * 解析其他常见格式
     */
    function parseOtherFormats(dateString: string): string | null {
      const patterns = [
        // ISO 8601 格式
        /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3})?(?:Z|[+-]\d{2}:\d{2})?)/,
        // RFC 2822 格式
        /\w{3},?\s+\d{1,2}\s+\w{3}\s+\d{4}\s+\d{1,2}:\d{2}:\d{2}/,
        // 时间戳（10位秒级）
        /^(\d{10})$/,
        // 时间戳（13位毫秒级）
        /^(\d{13})$/
      ];
      
      for (const pattern of patterns) {
        const match = dateString.match(pattern);
        if (match) {
          if (match[1] && /^\d{10}$/.test(match[1])) {
            // 10位时间戳
            const date = new Date(parseInt(match[1]) * 1000);
            if (isValidDate(date)) return formatDate(date);
          } else if (match[1] && /^\d{13}$/.test(match[1])) {
            // 13位时间戳
            const date = new Date(parseInt(match[1]));
            if (isValidDate(date)) return formatDate(date);
          } else {
            const date = new Date(match[0]);
            if (isValidDate(date)) return formatDate(date);
          }
        }
      }
      
      return null;
    }

    /**
     * 验证日期对象是否有效
     */
    function isValidDate(date: Date): boolean {
      return date instanceof Date && !isNaN(date.getTime());
    }

    /**
     * 验证发布日期是否合理
     */
    function isValidPublishDate(dateString: string): boolean {
      const date = new Date(dateString);
      const now = new Date();
      const tenYearsAgo = new Date(now.getFullYear() - 10, now.getMonth(), now.getDate());
      
      // 发布日期应该在过去10年内且不能是未来时间
      return date >= tenYearsAgo && date <= now;
    }

    /**
     * 格式化日期为标准格式
     */
    function formatDate(date: Date): string {
      return date.toISOString().split('T')[0]; // 返回 YYYY-MM-DD 格式
    }

    /**
     * 验证作者名称是否有效
     */
    function isValidAuthorName(author: string): boolean {
      if (!author || author.length === 0) return false;
      
      // 长度限制
      if (author.length > 100) return false;
      
      // 排除一些明显不是作者名的文本
      const excludePatterns = [
        /^(发布|发表|编辑|更新|修改|创建|时间|日期|作者|by|author|posted|published|created|updated|modified)$/i,
        /^(admin|administrator|系统管理员|管理员|匿名|anonymous|guest|用户|user)$/i,
        /^(未知|unknown|暂无|无|null|undefined|none)$/i,
        /^\d+$/, // 纯数字
        /^[^\u4e00-\u9fa5a-zA-Z]+$/, // 不包含中文或英文字母
        /^(.)\1{2,}$/, // 重复字符
        /^(.*\s){5,}/, // 包含太多空格
        /@.*\./, // 邮箱格式
        /^https?:\/\//, // URL格式
        /^\d{4}-\d{2}-\d{2}/, // 日期格式
        /阅读|点赞|收藏|分享|评论|关注|粉丝|文章|博客|网站|平台/
      ];

      return !excludePatterns.some(pattern => pattern.test(author));
    }
    /**
     * 提取完整的网页信息
     */
    async function extractPageData(aiConfig?: any) {
      const contentData = await extractMainContentAsHTML(aiConfig);
      
      // 如果小红书特定提取返回了封面图片，优先使用它
      const coverImage = contentData.coverImage || extractCoverImage();
      
      const author = extractAuthorInfo();
      const publishDate = extractPublishDate();
      
      // 使用推特自定义标题（如果有的话）
      const pageTitle = contentData.title || document.title || '无标题';
      
      const data = {
        title: pageTitle,
        site: getSiteInfo(),
        url: window.location.href,
        timestamp: new Date().toLocaleString('zh-CN'),
        content: contentData.markdown, // 使用Markdown格式
        contentHTML: contentData.html, // 保留HTML格式
        favicon: getFavicon(),
        coverImage: coverImage, // 优先使用小红书特定的封面图片
        author: author, // 添加作者信息
        publishDate: publishDate, // 添加发布日期
        aiAnalysis: contentData.aiAnalysis // 添加AI分析结果
      };

      console.log('提取的网页数据:', data);
      return data;
    }

    // 选择模式已迁移到 lib/content/selection

    // initInteractiveSelection 已迁移

    // createSelectionOverlay 已迁移

    // createSelectionPanel 已迁移

    /**
     * 设置选择事件监听器
     */
    // 选择事件绑定已迁移到 lib/content/selection

    /**
     * 开启选择模式
     */
    // startSelectionMode 已迁移

    /**
     * 关闭选择模式
     */
    // stopSelectionMode 已迁移

    /**
     * 鼠标悬停处理
     */
    // handleMouseOver 已迁移

    /**
     * 鼠标离开处理
     */
    // handleMouseOut 已迁移

    /**
     * 点击处理
     */
    // handleClick 已迁移

    /**
     * 键盘事件处理
     */
    // handleKeyDown 已迁移

    /**
     * 查找可选择的元素
     */
    // findSelectableElement 已迁移

    /**
     * 判断元素是否可选择
     */
    // isSelectableElement 已迁移

    /**
     * 判断是否为选择UI元素
     */
    // isSelectionUIElement 已迁移

    /**
     * 高亮元素
     */
    // highlightElement 已迁移

    /**
     * 清除高亮
     */
    // clearHighlight 已迁移

    /**
     * 切换元素选择状态
     */
    // toggleElementSelection 已迁移

    /**
     * 标记元素为已选择
     */
    // markElementAsSelected 已迁移

    /**
     * 取消元素选择标记
     */
    // unmarkElementAsSelected 已迁移

    /**
     * 重新编号已选择的元素
     */
    // renumberSelectedElements 已迁移

    /**
     * 生成元素ID
     */
    // generateElementId 已迁移

    /**
     * 清除所有已选择的元素
     */
    // clearSelectedElements 已迁移

    /**
     * 更新选择计数
     */
    // updateSelectionCount 已迁移

    /**
     * 确认选择
     */
    // confirmSelection 已迁移

    /**
     * 清除选择
     */
    // clearSelection 已迁移

    /**
     * 取消选择
     */
    // cancelSelection 已迁移

    /**
     * 提取选中的内容
     */
    // extractSelectedContent 已迁移

    // 选择系统由模块完全承载（lib/content/selection）

    // 监听来自background或sidepanel的消息（委托到拆分模块）
    browser.runtime.onMessage.addListener(handleContentMessages)

    // downloadImageInContext 已迁移至 lib/content/image-download

    /**
     * 通过创建img元素下载图片（绕过CORS限制）
     */
    // downloadImageViaElement 已迁移至 lib/content/image-download

    /**
     * 模拟右键另存为的方式下载图片
     */
    // downloadImageViaRightClick 已移除（不再使用）

    /**
     * 通过background script下载图片（绕过content script的限制）
     */
    async function downloadImageViaBackground(imageUrl: string): Promise<{ success: boolean; blob?: string; contentType?: string; error?: string }> {
      try {
        console.log('📡 通过background script下载图片:', imageUrl);
        
        // 向background script发送下载请求
        const response = await browser.runtime.sendMessage({
          type: 'DOWNLOAD_IMAGE_VIA_BACKGROUND',
          imageUrl: imageUrl,
          referer: 'https://m.weibo.cn/'
        });
        
        if (response && response.success) {
          console.log('✅ background script下载成功');
          return response;
        } else {
          console.log('❌ background script下载失败:', response?.error);
          return {
            success: false,
            error: response?.error || 'background script下载失败'
          };
        }
      } catch (error) {
        console.error('❌ background script通信失败:', error);
        return {
          success: false,
          error: `background script通信失败: ${error instanceof Error ? error.message : '未知错误'}`
        };
      }
    }

    /**
     * 获取页面中的所有图片
     */
    // getAllPageImages 将由模块按需实现

    // 预加载缓存
    // 预加载缓存与逻辑迁移到模块

    /**
     * 预加载图片，建立浏览器会话和缓存
     */
    // preloadImages 已迁移至 lib/content/image-download

    /**
     * 获取预加载的图片数据
     */
    // getPreloadedImageData 已迁移至 lib/content/image-download

    // SPA 路由监听交由模块托管，变更时自动提取并发送到 sidepanel（带去抖与去重保护）
    let lastExtractedUrl: string | null = null
    let lastExtractedAt = 0
    let isExtracting = false
    const DEBOUNCE_MS = 800
    let debounceTimer: number | undefined

    const scheduleAutoExtract = () => {
      if (debounceTimer) window.clearTimeout(debounceTimer)
      debounceTimer = window.setTimeout(runAutoExtractIfNeeded, DEBOUNCE_MS)
    }

    const runAutoExtractIfNeeded = async () => {
      const now = Date.now()
      const currentUrl = window.location.href
      if (isExtracting) return
      // 相同 URL 且 30s 内不重复提取
      if (lastExtractedUrl === currentUrl && now - lastExtractedAt < 30000) return
      isExtracting = true
      try {
        const data = await extractPageData()
        await browser.runtime.sendMessage({ type: 'SELECTED_CONTENT', data })
        lastExtractedUrl = currentUrl
        lastExtractedAt = now
      } catch (e) {
        console.warn('SPA变更后自动提取失败:', e)
      } finally {
        isExtracting = false
      }
    }

    initSpaWatch(() => {
      scheduleAutoExtract()
    })

    // 页面加载完成后自动提取数据（可选）
    if (document.readyState === 'complete') {
      console.log('页面已加载完成，准备提取数据');
    } else {
      window.addEventListener('load', () => {
        console.log('页面加载完成，准备提取数据');
      });
    }
  },
});
