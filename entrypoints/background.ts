export default defineBackground(() => {
  console.log('Background script loaded!', { id: browser.runtime.id });

  // Handle extension icon click to open sidepanel
  browser.action.onClicked.addListener(async (tab) => {
    if (tab.id) {
      await browser.sidePanel.open({ tabId: tab.id });
    }
  });

  // Set up sidepanel options on install
  browser.runtime.onInstalled.addListener(async () => {
    await browser.sidePanel.setOptions({
      path: 'sidepanel.html',
      enabled: true
    });
    
    await browser.sidePanel.setPanelBehavior({
      openPanelOnActionClick: true
    });
  });

  // Handle Notion API calls from sidepanel
  browser.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('Background received message:', message);

    if (message.type === 'NOTION_API_CALL') {
      // 异步处理API调用
      (async () => {
        try {
          const { endpoint, method, headers, body } = message;
          
          console.log('Making Notion API request:', { endpoint, method });
          
          const fetchOptions: RequestInit = {
            method: method || 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Notion-Version': '2022-06-28',
              ...headers
            }
          };

          if (body) {
            fetchOptions.body = JSON.stringify(body);
          }

          const response = await fetch(endpoint, fetchOptions);
          
          // 尝试解析JSON，如果失败则使用文本
          let data;
          try {
            data = await response.json();
          } catch (parseError) {
            console.warn('Failed to parse JSON response:', parseError);
            data = await response.text();
          }

          if (!response.ok) {
            const errorMessage = typeof data === 'object' && data.message 
              ? data.message 
              : `HTTP ${response.status}: ${response.statusText}`;
            
            console.error('Notion API error:', { status: response.status, data });
            sendResponse({ 
              success: false, 
              error: errorMessage 
            });
            return;
          }

          console.log('Notion API success:', data);
          sendResponse({ success: true, data });
          
        } catch (error) {
          console.error('Notion API call failed:', error);
          sendResponse({ 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
        }
      })();
      
      return true; // 保持消息通道开放用于异步响应
    }

    if (message.type === 'DOWNLOAD_IMAGE_VIA_BACKGROUND') {
      // 通过background script下载图片，绕过content script的CORS限制
      (async () => {
        try {
          const { imageUrl, referer } = message;
          console.log('Background script 开始下载图片:', imageUrl, '使用referer:', referer);
          
          const fetchOptions: RequestInit = {
            method: 'GET',
            headers: {
              'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
              'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
              'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache'
            }
          };

          // 如果提供了referer，添加到headers中
          if (referer) {
            fetchOptions.headers = {
              ...fetchOptions.headers,
              'Referer': referer
            };
          }

          const response = await fetch(imageUrl, fetchOptions);
          
          if (!response.ok) {
            console.error('Background script 图片下载失败:', response.status, response.statusText);
            sendResponse({ 
              success: false, 
              error: `HTTP ${response.status}: ${response.statusText}` 
            });
            return;
          }

          const blob = await response.blob();
          console.log('Background script 图片下载成功:', blob.size, 'bytes');

          // 将blob转换为base64
          const reader = new FileReader();
          reader.onload = () => {
            const base64 = (reader.result as string).split(',')[1];
            sendResponse({
              success: true,
              blob: base64,
              contentType: blob.type
            });
          };
          reader.onerror = () => {
            sendResponse({
              success: false,
              error: 'Failed to convert blob to base64'
            });
          };
          reader.readAsDataURL(blob);
          
        } catch (error) {
          console.error('Background script 图片下载异常:', error);
          sendResponse({ 
            success: false, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          });
        }
      })();
      
      return true; // 保持消息通道开放用于异步响应
    }

    return false;
  });
});
