import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { useConfig } from '@/hooks/use-config'
import { AlertCircle, Bot, Camera, FileText, RotateCcw, Check, X, Upload, Download, FolderOpen, Settings, Palette, ArrowLeft } from 'lucide-react'
import { useState } from 'react'

export default function SettingsPage() {
  const { config, loading, error, updateAI, updateImage, updateNotion, updateLocalSave, updateExport, updateRules, resetConfig, exportConfig, importConfig, validateConfig } = useConfig()
  const [showApiKey, setShowApiKey] = useState(false)
  const [showSecrets, setShowSecrets] = useState(false)

  // 返回剪藏页面
  const handleGoToClip = () => {
    const event = new CustomEvent('switchToClip')
    document.dispatchEvent(event)
  }
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [validationSuccess, setValidationSuccess] = useState<string[]>([])
  const [isValidating, setIsValidating] = useState(false)
  const [imageValidationErrors, setImageValidationErrors] = useState<string[]>([])
  const [imageValidationSuccess, setImageValidationSuccess] = useState<string[]>([])
  const [isValidatingImage, setIsValidatingImage] = useState(false)
  const [notionValidationErrors, setNotionValidationErrors] = useState<string[]>([])
  const [notionValidationSuccess, setNotionValidationSuccess] = useState<string[]>([])
  const [isValidatingNotion, setIsValidatingNotion] = useState(false)
  const [aiValidationErrors, setAiValidationErrors] = useState<string[]>([])
  const [aiValidationSuccess, setAiValidationSuccess] = useState<string[]>([])
  const [isValidatingAI, setIsValidatingAI] = useState(false)

  const handleAIValidation = async () => {
    setIsValidatingAI(true)
    setAiValidationErrors([])
    setAiValidationSuccess([])
    
    try {
      console.log('开始AI配置验证')
      console.log('AI配置:', { 
        enabled: config.ai.enabled, 
        provider: config.ai.provider,
        hasApiKey: !!config.ai.apiKey,
        apiKeyLength: config.ai.apiKey?.length,
        baseUrl: config.ai.baseUrl,
        model: config.ai.model
      })

      // 测试AI连接
      if (config.ai.enabled) {
        try {
          console.log('调用testAIConnection...')
          const result = await testAIConnection()
          console.log('testAIConnection成功:', result)
          setAiValidationSuccess([result.message])
        } catch (error) {
          console.error('testAIConnection失败:', error)
          const errorMessage = error instanceof Error ? error.message : '未知错误'
          setAiValidationErrors([`AI 服务测试失败: ${errorMessage}`])
        }
      } else {
        setAiValidationErrors(['AI 服务未启用'])
      }
    } catch (err) {
      console.error('AI 配置验证失败:', err)
      setAiValidationErrors(['AI 配置验证失败'])
    } finally {
      setIsValidatingAI(false)
    }
  }

  const handleNotionValidation = async () => {
    setIsValidatingNotion(true)
    setNotionValidationErrors([])
    setNotionValidationSuccess([])
    
    try {
      console.log('开始Notion配置验证')
      console.log('Notion配置:', { 
        enabled: config.export.notion.enabled, 
        hasToken: !!config.export.notion.token,
        tokenLength: config.export.notion.token?.length,
        databaseId: config.export.notion.databaseId 
      })

      // 测试Notion连接
      if (config.export.notion.enabled) {
        try {
          console.log('调用testNotionConnection...')
          await testNotionConnection()
          console.log('testNotionConnection成功')
          setNotionValidationSuccess(['Notion 服务连接测试成功'])
        } catch (error) {
          console.error('testNotionConnection失败:', error)
          const errorMessage = error instanceof Error ? error.message : '未知错误'
          setNotionValidationErrors([`Notion 服务测试失败: ${errorMessage}`])
        }
      } else {
        setNotionValidationErrors(['Notion 服务未启用'])
      }
    } catch (err) {
      console.error('Notion 配置验证失败:', err)
      setNotionValidationErrors(['Notion 配置验证失败'])
    } finally {
      setIsValidatingNotion(false)
    }
  }

  const handleImageValidation = async () => {
    setIsValidatingImage(true)
    setImageValidationErrors([])
    setImageValidationSuccess([])
    
    try {
      // 测试图床连接
      if (config.image.enabled) {
        try {
          await testImageUploadConnection()
          setImageValidationSuccess(['图床服务连接测试成功'])
        } catch (error) {
          setImageValidationErrors([`图床服务测试失败: ${error instanceof Error ? error.message : '未知错误'}`])
        }
      } else {
        setImageValidationErrors(['图床服务未启用'])
      }
    } catch (err) {
      console.error('图床配置验证失败:', err)
      setImageValidationErrors(['图床配置验证失败'])
    } finally {
      setIsValidatingImage(false)
    }
  }

  const handleValidation = async () => {
    setIsValidating(true)
    setValidationErrors([])
    setValidationSuccess([])
    
    try {
      const errors = validateConfig()
      const successMessages: string[] = []
      
      if (errors.length > 0) {
        setValidationErrors(errors)
        return false
      }
      
      // 测试各个服务的连接
      if (config.image.enabled) {
        try {
          await testImageUploadConnection()
          successMessages.push('图床服务连接测试成功')
        } catch (error) {
          setValidationErrors(prev => [...prev, `图床服务测试失败: ${error instanceof Error ? error.message : '未知错误'}`])
        }
      }
      
      if (config.ai.enabled) {
        try {
          await testAIConnection()
          successMessages.push('AI 服务连接测试成功')
        } catch (error) {
          setValidationErrors(prev => [...prev, `AI 服务测试失败: ${error instanceof Error ? error.message : '未知错误'}`])
        }
      }
      
      if (config.export.notion.enabled) {
        try {
          await testNotionConnection()
          successMessages.push('Notion 服务连接测试成功')
        } catch (error) {
          setValidationErrors(prev => [...prev, `Notion 服务测试失败: ${error instanceof Error ? error.message : '未知错误'}`])
        }
      }
      
      if (successMessages.length > 0) {
        setValidationSuccess(successMessages)
      }
      
      if (validationErrors.length === 0) {
        setValidationSuccess(prev => [...prev, '所有配置验证通过'])
      }
      
      return errors.length === 0
    } catch (err) {
      console.error('配置验证失败:', err)
      setValidationErrors(['配置验证失败'])
      return false
    } finally {
      setIsValidating(false)
    }
  }
  
  const testImageUploadConnection = async () => {
    try {
      const { ImageUploadManager } = await import('@/lib/image-uploader')
      const uploader = new ImageUploadManager(config.image)
      const result = await uploader.testConnection()
      
      if (result.success) {
        return
      } else {
        throw new Error(result.error || '连接测试失败')
      }
    } catch (error) {
      throw error
    }
  }
  
  const testAIConnection = async () => {
    try {
      console.log('开始AI连接测试...')
      console.log('AI配置:', { 
        enabled: config.ai.enabled, 
        provider: config.ai.provider,
        hasApiKey: !!config.ai.apiKey,
        apiKeyLength: config.ai.apiKey?.length,
        baseUrl: config.ai.baseUrl,
        model: config.ai.model
      })

      // 使用 LangChain 适配层进行测试
      const { testModelConnection } = await import('@/lib/llm')
      const result = await testModelConnection(config.ai)
      
      if (result.success) {
        console.log('AI测试成功:', result.message)
        return {
          success: true,
          message: result.message || 'AI 服务连接测试成功'
        }
      } else {
        console.error('AI测试失败:', result.error)
        throw new Error(result.error || 'AI 服务连接测试失败')
      }

    } catch (error) {
      console.error('AI连接测试失败:', error)
      throw error
    }
  }
  
  const testNotionConnection = async () => {
    try {
      console.log('开始导入notion-api-client模块...')
      const { createExtensionNotionClient } = await import('@/lib/notion-api-client')
      console.log('模块导入成功，创建客户端...')
      
      const notionClient = createExtensionNotionClient(config.export.notion)
      console.log('客户端创建成功，开始测试连接...')
      
      const result = await notionClient.testConnection()
      console.log('连接测试结果:', result)
      
      if (result.success) {
        console.log('连接成功，开始获取数据库信息...')
        // 尝试获取数据库信息以验证数据库访问权限
        const databaseInfo = await notionClient.getDatabaseInfo()
        console.log('数据库信息获取结果:', databaseInfo)
        
        if (!databaseInfo) {
          throw new Error('无法访问指定的数据库，请检查数据库ID和权限设置')
        }
        console.log('数据库访问验证成功')
        return
      } else {
        throw new Error(result.error || '连接测试失败')
      }
    } catch (error) {
      console.error('testNotionConnection异常:', error)
      throw error
    }
  }

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const configData = JSON.parse(e.target?.result as string)
          importConfig(configData)
        } catch (err) {
          console.error('Failed to parse config file:', err)
        }
      }
      reader.readAsText(file)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <p className="text-muted-foreground">Loading...</p>
      </div>
    )
  }

  return (
    <ScrollArea className="h-full">
      <div className="space-y-6 p-4">
        {/* 返回按钮 */}
        <div className="flex items-center gap-3 pb-4 border-b">
          <Button
            onClick={handleGoToClip}
            variant="ghost"
            size="sm"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            返回剪藏
          </Button>
          <div>
            <h1 className="text-lg font-semibold">设置</h1>
            <p className="text-sm text-muted-foreground">配置AI、图床和导出服务</p>
          </div>
        </div>

        {error && (
          <div className="flex items-center gap-2 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <AlertCircle className="h-4 w-4 text-destructive" />
            <span className="text-sm text-destructive">{error}</span>
          </div>
        )}

        <Tabs defaultValue="rules" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="rules" className="flex items-center gap-2">
              <Palette className="h-4 w-4" />
              规则
            </TabsTrigger>
            <TabsTrigger value="ai" className="flex items-center gap-2">
              <Bot className="h-4 w-4" />
              AI
            </TabsTrigger>
            <TabsTrigger value="image" className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              图床
            </TabsTrigger>
            <TabsTrigger value="export" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              导出
            </TabsTrigger>
          </TabsList>

          <TabsContent value="rules" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Palette className="h-5 w-5" />
                  自定义规则
                </CardTitle>
                <CardDescription>
                  配置内容处理规则和过滤选项
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">移除表情符号图片</Label>
                      <p className="text-xs text-muted-foreground">
                        自动检测并移除正文中以图片链接形式存在的表情符号（如微信表情、QQ表情等）
                      </p>
                    </div>
                    <Switch 
                      checked={config.rules?.removeEmojiImages ?? true}
                      onCheckedChange={(checked) => updateRules({ removeEmojiImages: checked })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="ai" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  AI 配置
                  <Badge variant={config.ai.enabled ? "default" : "secondary"}>
                    {config.ai.enabled ? "已启用" : "未启用"}
                  </Badge>
                  {aiValidationSuccess.length > 0 && (
                    <Badge variant="outline" className="text-green-700 border-green-300 bg-green-50">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      已连接
                    </Badge>
                  )}
                  {aiValidationErrors.length > 0 && (
                    <Badge variant="outline" className="text-red-700 border-red-300 bg-red-50">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      连接失败
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  配置AI辅助内容提取功能
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">启用AI辅助</Label>
                    <p className="text-xs text-muted-foreground">
                      使用AI改善内容提取质量
                    </p>
                  </div>
                  <Switch
                    checked={config.ai.enabled}
                    onCheckedChange={(checked) => updateAI({ enabled: checked })}
                  />
                </div>

                {config.ai.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">AI服务商</Label>
                      <Select
                        value={config.ai.provider}
                        onValueChange={(value) => updateAI({ provider: value as 'openai' | 'claude' | 'deepseek' | 'kimi' | 'openrouter' | 'custom' })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="openai">OpenAI</SelectItem>
                          <SelectItem value="claude">Claude</SelectItem>
                          <SelectItem value="deepseek">DeepSeek</SelectItem>
                          <SelectItem value="kimi">Kimi</SelectItem>
                          <SelectItem value="openrouter">OpenRouter</SelectItem>
                          <SelectItem value="custom">Custom</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">API Key</Label>
                      <div className="flex gap-2">
                        <Input
                          type={showApiKey ? "text" : "password"}
                          value={config.ai.apiKey}
                          onChange={(e) => updateAI({ apiKey: e.target.value })}
                          placeholder="sk-..."
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowApiKey(!showApiKey)}
                        >
                          {showApiKey ? <X className="h-4 w-4" /> : <Check className="h-4 w-4" />}
                        </Button>
                      </div>
                    </div>

                    {(config.ai.provider === 'custom' || config.ai.provider === 'deepseek' || config.ai.provider === 'kimi' || config.ai.provider === 'openrouter') && (
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">Base URL</Label>
                        <Input
                          value={config.ai.baseUrl || ''}
                          onChange={(e) => updateAI({ baseUrl: e.target.value })}
                          placeholder={
                            config.ai.provider === 'deepseek' ? 'api.deepseek.com' :
                            config.ai.provider === 'kimi' ? 'api.moonshot.cn' :
                            config.ai.provider === 'openrouter' ? 'openrouter.ai/api/v1' :
                            'api.example.com'
                          }
                          className="text-sm"
                        />
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">模型</Label>
                      <Input
                        value={config.ai.model}
                        onChange={(e) => updateAI({ model: e.target.value })}
                        placeholder={
                          config.ai.provider === 'openai' ? 'gpt-3.5-turbo' :
                          config.ai.provider === 'claude' ? 'claude-3-haiku-20240307' :
                          config.ai.provider === 'deepseek' ? 'deepseek-chat' :
                          config.ai.provider === 'kimi' ? 'moonshot-v1-8k' :
                          config.ai.provider === 'openrouter' ? 'openai/gpt-3.5-turbo' :
                          'gpt-3.5-turbo'
                        }
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">温度 ({config.ai.temperature})</Label>
                        <Input
                          type="range"
                          min="0"
                          max="1"
                          step="0.1"
                          value={config.ai.temperature}
                          onChange={(e) => updateAI({ temperature: parseFloat(e.target.value) })}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label className="text-sm font-medium">最大令牌数</Label>
                        <Input
                          type="number"
                          value={config.ai.maxTokens}
                          onChange={(e) => updateAI({ maxTokens: parseInt(e.target.value) })}
                          min="100"
                          max="8000"
                        />
                      </div>
                    </div>
                    
                    {/* AI配置验证按钮和结果 */}
                    <div className="pt-4 border-t space-y-3">
                      <Button 
                        onClick={handleAIValidation} 
                        disabled={isValidatingAI}
                        variant="outline"
                        size="sm"
                        className="w-full"
                      >
                        {isValidatingAI ? (
                          <>
                            <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                            验证AI配置...
                          </>
                        ) : (
                          <>
                            <Check className="w-4 h-4 mr-2" />
                            验证AI配置
                          </>
                        )}
                      </Button>
                      
                      {/* AI验证成功提示 */}
                      {aiValidationSuccess.length > 0 && (
                        <div className="space-y-2">
                          {aiValidationSuccess.map((message, index) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                              <Check className="h-4 w-4 text-green-600" />
                              <span className="text-sm text-green-800">{message}</span>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* AI验证错误提示 */}
                      {aiValidationErrors.length > 0 && (
                        <div className="space-y-2">
                          {aiValidationErrors.map((error, index) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-md">
                              <AlertCircle className="h-4 w-4 text-red-600" />
                              <span className="text-sm text-red-800">{error}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="image" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  图床配置
                  <Badge variant={config.image.enabled ? "default" : "secondary"}>
                    {config.image.enabled ? "已启用" : "未启用"}
                  </Badge>
                  {imageValidationSuccess.length > 0 && (
                    <Badge variant="outline" className="text-green-700 border-green-300 bg-green-50">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      已连接
                    </Badge>
                  )}
                  {imageValidationErrors.length > 0 && (
                    <Badge variant="outline" className="text-red-700 border-red-300 bg-red-50">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      连接失败
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  配置图片上传和存储服务
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">启用图床</Label>
                    <p className="text-xs text-muted-foreground">
                      自动上传图片到云存储
                    </p>
                  </div>
                  <Switch
                    checked={config.image.enabled}
                    onCheckedChange={(checked) => updateImage({ enabled: checked })}
                  />
                </div>

                {config.image.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">图床服务商</Label>
                      <Select
                        value={config.image.provider}
                        onValueChange={(value) => updateImage({ provider: value as 'alioss' | 'qiniu' | 's3' })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="alioss">阿里云 OSS</SelectItem>
                          <SelectItem value="qiniu">七牛云</SelectItem>
                          <SelectItem value="s3">Amazon S3</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Access Key</Label>
                          <Input
                            type={showSecrets ? "text" : "password"}
                            value={config.image.accessKey || ''}
                            onChange={(e) => updateImage({ accessKey: e.target.value })}
                            placeholder="Your access key"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Secret Key</Label>
                          <Input
                            type={showSecrets ? "text" : "password"}
                            value={config.image.secretKey || ''}
                            onChange={(e) => updateImage({ secretKey: e.target.value })}
                            placeholder="Your secret key"
                          />
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setShowSecrets(!showSecrets)}
                        >
                          {showSecrets ? <X className="h-4 w-4" /> : <Check className="h-4 w-4" />}
                        </Button>
                        <span className="text-sm text-muted-foreground">
                          {showSecrets ? "隐藏" : "显示"}密钥
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Bucket</Label>
                          <Input
                            value={config.image.bucket || ''}
                            onChange={(e) => updateImage({ bucket: e.target.value })}
                            placeholder="your-bucket"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">区域</Label>
                          <Input
                            value={config.image.region || ''}
                            onChange={(e) => updateImage({ region: e.target.value })}
                            placeholder={config.image.provider === 's3' ? 'us-west-2' : 'oss-cn-beijing'}
                          />
                        </div>
                      </div>

                      {config.image.provider === 's3' && (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">Endpoint (可选)</Label>
                          <Input
                            value={config.image.endpoint || ''}
                            onChange={(e) => updateImage({ endpoint: e.target.value })}
                            placeholder="s3.amazonaws.com"
                            className="text-sm"
                          />
                          <p className="text-xs text-muted-foreground">
                            留空使用默认 AWS S3 端点，或填写兼容 S3 的自定义端点
                          </p>
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">自定义域名 (可选)</Label>
                        <Input
                          value={config.image.domain || ''}
                          onChange={(e) => updateImage({ domain: e.target.value })}
                          placeholder="your-domain.com"
                          className="text-sm"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label className="text-sm font-medium">存储路径</Label>
                        <Input
                          value={config.image.path || ''}
                          onChange={(e) => updateImage({ path: e.target.value })}
                          placeholder="images/"
                        />
                      </div>
                      
                      {/* 图床配置验证按钮和结果 */}
                      <div className="pt-4 border-t space-y-3">
                        <Button 
                          onClick={handleImageValidation} 
                          disabled={isValidatingImage}
                          variant="outline"
                          size="sm"
                          className="w-full"
                        >
                          {isValidatingImage ? (
                            <>
                              <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                              验证图床配置...
                            </>
                          ) : (
                            <>
                              <Check className="w-4 h-4 mr-2" />
                              验证图床配置
                            </>
                          )}
                        </Button>
                        
                        {/* 图床验证成功提示 */}
                        {imageValidationSuccess.length > 0 && (
                          <div className="space-y-2">
                            {imageValidationSuccess.map((message, index) => (
                              <div key={index} className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                                <Check className="h-4 w-4 text-green-600" />
                                <span className="text-sm text-green-800">{message}</span>
                              </div>
                            ))}
                          </div>
                        )}

                        {/* 图床验证错误提示 */}
                        {imageValidationErrors.length > 0 && (
                          <div className="space-y-2">
                            {imageValidationErrors.map((error, index) => (
                              <div key={index} className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-md">
                                <AlertCircle className="h-4 w-4 text-red-600" />
                                <span className="text-sm text-red-800">{error}</span>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="export" className="space-y-6">
            {/* Notion配置卡片 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Notion 同步
                  <Badge variant={config.export.notion.enabled ? "default" : "secondary"}>
                    {config.export.notion.enabled ? "已启用" : "未启用"}
                  </Badge>
                  {notionValidationSuccess.length > 0 && (
                    <Badge variant="outline" className="text-green-700 border-green-300 bg-green-50">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      已连接
                    </Badge>
                  )}
                  {notionValidationErrors.length > 0 && (
                    <Badge variant="outline" className="text-red-700 border-red-300 bg-red-50">
                      <AlertCircle className="h-3 w-3 mr-1" />
                      连接失败
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  配置Notion集成，自动同步剪藏内容到Notion数据库。
                  <br />
                  <a 
                    href="https://developers.notion.com/docs/create-a-notion-integration" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-primary hover:underline text-xs break-all"
                  >
                    如何创建Notion Integration →
                  </a>
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">启用Notion同步</Label>
                    <p className="text-xs text-muted-foreground">
                      自动将剪藏内容同步到Notion
                    </p>
                  </div>
                  <Switch
                    checked={config.export.notion.enabled}
                    onCheckedChange={(checked) => updateNotion({ enabled: checked })}
                  />
                </div>

                {config.export.notion.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Integration Token</Label>
                      <Textarea
                        value={config.export.notion.token}
                        onChange={(e) => updateNotion({ token: e.target.value })}
                        placeholder="secret_..."
                        rows={3}
                      />
                      <p className="text-xs text-muted-foreground">
                        1. 在Notion中创建Integration获取Token<br />
                        2. 确保Integration有访问目标数据库的权限<br />
                        3. Token格式: <span className="break-all">secret_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx</span>
                      </p>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Database ID</Label>
                      <Input
                        value={config.export.notion.databaseId}
                        onChange={(e) => updateNotion({ databaseId: e.target.value })}
                        placeholder="数据库ID (32位)"
                        className="text-sm"
                      />
                      <p className="text-xs text-muted-foreground">
                        从Notion数据库URL中获取ID (32位字符，包含短横线)<br />
                        <span className="break-all">
                          例如: notion.so/database-name-<strong>a1b2c3d4...xyz</strong>
                        </span>
                      </p>
                    </div>
                    
                    {/* Notion 配置验证按钮和结果 */}
                    <div className="pt-4 border-t space-y-3">
                      <Button 
                        onClick={handleNotionValidation} 
                        disabled={isValidatingNotion}
                        variant="outline"
                        size="sm"
                        className="w-full"
                      >
                        {isValidatingNotion ? (
                          <>
                            <div className="w-4 h-4 border-2 border-gray-400 border-t-transparent rounded-full animate-spin mr-2" />
                            验证Notion配置...
                          </>
                        ) : (
                          <>
                            <Check className="w-4 h-4 mr-2" />
                            验证Notion配置
                          </>
                        )}
                      </Button>
                      
                      {/* Notion验证成功提示 */}
                      {notionValidationSuccess.length > 0 && (
                        <div className="space-y-2">
                          {notionValidationSuccess.map((message, index) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                              <Check className="h-4 w-4 text-green-600" />
                              <span className="text-sm text-green-800">{message}</span>
                            </div>
                          ))}
                        </div>
                      )}

                      {/* Notion验证错误提示 */}
                      {notionValidationErrors.length > 0 && (
                        <div className="space-y-2">
                          {notionValidationErrors.map((error, index) => (
                            <div key={index} className="flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-md">
                              <AlertCircle className="h-4 w-4 text-red-600" />
                              <span className="text-sm text-red-800">{error}</span>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 本地保存配置卡片 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FolderOpen className="h-5 w-5" />
                  本地保存
                  <Badge variant={config.export.localSave.enabled ? "default" : "secondary"}>
                    {config.export.localSave.enabled ? "已启用" : "未启用"}
                  </Badge>
                </CardTitle>
                <CardDescription>
                  配置本地文件保存设置，自定义下载目录。
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <Label className="text-sm font-medium">启用本地保存</Label>
                    <p className="text-xs text-muted-foreground">
                      允许下载剪藏内容为本地文件
                    </p>
                  </div>
                  <Switch
                    checked={config.export.localSave.enabled}
                    onCheckedChange={(checked) => updateLocalSave({ enabled: checked })}
                  />
                </div>

                {config.export.localSave.enabled && (
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">保存目录</Label>
                      <div className="flex gap-2">
                        <Input
                          value={config.export.localSave.saveDirectory || ''}
                          onChange={(e) => updateLocalSave({ saveDirectory: e.target.value })}
                          placeholder="留空使用浏览器默认下载目录"
                          className="flex-1"
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={async () => {
                            // 检查是否支持 File System Access API
                            if ('showDirectoryPicker' in window) {
                              try {
                                const dirHandle = await (window as any).showDirectoryPicker()
                                updateLocalSave({ saveDirectory: dirHandle.name })
                              } catch (error) {
                                if ((error as Error).name !== 'AbortError') {
                                  console.error('选择目录失败:', error)
                                  alert('选择目录失败，请手动输入目录路径')
                                }
                              }
                            } else {
                              alert('您的浏览器不支持目录选择功能，请手动输入目录路径。建议使用 Chrome 86+ 或 Edge 86+ 浏览器以获得最佳体验。')
                            }
                          }}
                        >
                          <FolderOpen className="h-4 w-4" />
                        </Button>
                      </div>
                      <p className="text-xs text-muted-foreground break-words">
                        设置文件下载的目录路径。留空将使用浏览器的默认下载目录。<br />
                        <strong>现代浏览器支持：</strong>Chrome/Edge 86+支持直接保存到指定目录，其他浏览器将下载到默认目录。<br />
                        注意：首次使用时浏览器会请求文件系统访问权限。
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <Separator />

        {/* 全局验证结果 */}
        {validationSuccess.length > 0 && (
          <div className="space-y-2">
            {validationSuccess.map((message, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-md">
                <Check className="h-4 w-4 text-green-600" />
                <span className="text-sm text-green-800">{message}</span>
              </div>
            ))}
          </div>
        )}

        {validationErrors.length > 0 && (
          <div className="space-y-2">
            {validationErrors.map((error, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">{error}</span>
              </div>
            ))}
          </div>
        )}

        <div className="flex flex-col gap-4">
          <div className="flex flex-wrap gap-2">
            <Button 
              onClick={handleValidation} 
              disabled={isValidating}
              className="flex-1 min-w-24 flex items-center gap-2"
            >
              {isValidating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  验证中...
                </>
              ) : (
                '验证配置'
              )}
            </Button>
            <Button variant="outline" onClick={exportConfig} className="flex items-center gap-2 whitespace-nowrap">
              <Download className="h-4 w-4" />
              导出
            </Button>
            <Button variant="outline" asChild className="flex items-center gap-2 whitespace-nowrap">
              <label htmlFor="import-config" className="cursor-pointer">
                <Upload className="h-4 w-4" />
                导入
              </label>
            </Button>
            <input
              id="import-config"
              type="file"
              accept=".json"
              className="hidden"
              onChange={handleImport}
            />
          </div>

          <Button
            variant="outline"
            onClick={resetConfig}
            className="flex items-center gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            重置所有设置
          </Button>
        </div>
      </div>
    </ScrollArea>
  )
}