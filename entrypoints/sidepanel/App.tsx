import { useSettings } from '@/hooks/use-settings'
import { useTheme } from '@/hooks/use-theme'
import { useEffect, useState } from 'react'
import {
  Monitor,
  Moon,
  Sun
} from 'lucide-react'
import ClipPage from './ClipPage'
import SettingsPage from './SettingsPage'

function App() {
  const { appearance, ui, loading, updateAppearance, updateUI } = useSettings()
  const { resolvedTheme, setTheme } = useTheme({
    theme: appearance.theme,
    onThemeChange: (theme) => updateAppearance({ theme })
  })

  const [currentPage, setCurrentPage] = useState<'clip' | 'settings'>('clip')

  const themeOptions = [
    { value: 'system', label: 'System', icon: Monitor },
    { value: 'light', label: 'Light', icon: Sun },
    { value: 'dark', label: 'Dark', icon: Moon }
  ] as const

  // 监听来自ClipPage的设置页跳转事件
  useEffect(() => {
    const handleSwitchToSettings = () => {
      setCurrentPage('settings')
    }

    const handleSwitchToClip = () => {
      setCurrentPage('clip')
    }

    document.addEventListener('switchToSettings', handleSwitchToSettings)
    document.addEventListener('switchToClip', handleSwitchToClip)
    return () => {
      document.removeEventListener('switchToSettings', handleSwitchToSettings)
      document.removeEventListener('switchToClip', handleSwitchToClip)
    }
  }, [])

  if (loading) {
    return (
      <div className="flex flex-col h-screen bg-background">
        <div className="flex-1 flex items-center justify-center">
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen bg-background">
      {/* 根据当前页面状态显示不同内容 */}
      {currentPage === 'clip' ? <ClipPage /> : <SettingsPage />}
    </div>
  )
}

export default App
