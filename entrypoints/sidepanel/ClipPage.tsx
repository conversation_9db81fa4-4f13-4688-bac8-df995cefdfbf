import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Loader2 } from 'lucide-react'
import { useConfig } from '@/hooks/use-config'
import { useClipHistory } from '@/hooks/use-clip-history'
import EmptyState from '@/components/EmptyState'
import WelcomePage from '@/components/WelcomePage'
import TaskProgress from '@/components/TaskProgress'
import SelectionModeIndicator from '@/components/SelectionModeIndicator'
import ErrorCard from '@/components/ErrorCard'
import TopToolbar from '@/components/TopToolbar'
import EditableTitle from '@/components/EditableTitle'
import PropertiesSection from '@/components/PropertiesSection'
import ContentEditor from '@/components/ContentEditor'
import ExportSection from '@/components/ExportSection'
import { 
  generateAndCopyClipImage, 
  generateAndDownloadClipImage, 
  preprocessContentForImage,
  type ClipData as ImageClipData
} from '@/lib/image-generator'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import type { ClipDataForNotion } from '@/lib/notion-api-client'
import type { ClipData } from '@/hooks/use-clip-history'

// 使用从 hooks 导入的 ClipData 类型

// 定义友好错误信息的类型
interface FriendlyError {
  title: string
  message: string
  suggestions: string[]
}

export default function ClipPage() {
  const { config } = useConfig()
  const {
    history,
    loading: historyLoading,
    currentPageInfo,
    addClipToHistory,
    getLatestClip,
    getPageToShow,
    checkCurrentPage
  } = useClipHistory()
  
  const [clipData, setClipData] = useState<ClipData | null>(null)
  const [pageMode, setPageMode] = useState<'current' | 'latest' | 'welcome'>('welcome')
  const [loading, setLoading] = useState(false) // 快捷剪藏的loading状态
  const [aiLoading, setAiLoading] = useState(false) // AI剪藏的loading状态
  const [error, setError] = useState<string | FriendlyError | null>(null)
  const [copied, setCopied] = useState(false)
  const [currentPageUrl, setCurrentPageUrl] = useState<string | null>(null) // 当前活动标签页的URL
  
  // 图片生成状态
  const [isGeneratingImage, setIsGeneratingImage] = useState(false)
  const [imageCopied, setImageCopied] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)
  
  // 编辑状态
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [isEditingContent, setIsEditingContent] = useState(false)
  const [editedTitle, setEditedTitle] = useState('')
  const [editedContent, setEditedContent] = useState('')

  // 图片选择状态
  const [showImageSelector, setShowImageSelector] = useState(false)
  const [availableImages, setAvailableImages] = useState<string[]>([])
  const [isLoadingImages, setIsLoadingImages] = useState(false)
  const [customImageUrl, setCustomImageUrl] = useState('')

  // 图片转存状态
  const [isUploadingImages, setIsUploadingImages] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [uploadStatus, setUploadStatus] = useState('')
  const [uploadedImages, setUploadedImages] = useState<{ original: string; uploaded: string }[]>([])

  // Notion同步状态
  const [isSyncingToNotion, setIsSyncingToNotion] = useState(false)
  const [syncNotionStatus, setSyncNotionStatus] = useState('')
  const [notionSyncResult, setNotionSyncResult] = useState<{ pageId?: string; pageUrl?: string } | null>(null)

  // 标签编辑状态
  const [isEditingTags, setIsEditingTags] = useState(false)
  const [editedTags, setEditedTags] = useState('clippings')

  // 新增状态
  const [isInSelectionMode, setIsInSelectionMode] = useState(false)
  const [currentTask, setCurrentTask] = useState<{
    type: 'ai' | 'upload' | 'notion' | 'download'
    status: 'running' | 'success' | 'error' | 'cancelled'
    title: string
    description?: string
    progress?: number
    result?: any
  } | null>(null)
  const [showTaskProgress, setShowTaskProgress] = useState(false)

  // 新的状态管理
  const [saveDestination, setSaveDestination] = useState<'local' | 'image' | 'notion'>('local')
  const [saveSuccess, setSaveSuccess] = useState(false)
  const [previewExpanded, setPreviewExpanded] = useState(false)

  // 添加只读状态
  const [isRead, setIsRead] = useState(false)

  /**
   * 启动交互式选择模式
   */
  const startSelectionMode = async () => {
    try {
      // 获取当前活动标签页
      const [activeTab] = await browser.tabs.query({ 
        active: true, 
        currentWindow: true 
      })

      if (!activeTab || !activeTab.id) {
        const errorInfo = getFriendlyErrorMessage(new Error('无法获取当前标签页'))
        setError(errorInfo)
        return
      }

      // 向content script发送启动选择模式的消息
      await browser.tabs.sendMessage(activeTab.id, {
        type: 'START_SELECTION_MODE'
      })

      setIsInSelectionMode(true)
      console.log('已启动交互式选择模式')
    } catch (err) {
      console.error('启动选择模式失败:', err)
      const errorInfo = getFriendlyErrorMessage(err)
      setError(errorInfo)
    }
  }

  /**
   * 退出交互式选择模式
   */
  const exitSelectionMode = async () => {
    try {
      const [activeTab] = await browser.tabs.query({ 
        active: true, 
        currentWindow: true 
      })

      if (activeTab && activeTab.id) {
        await browser.tabs.sendMessage(activeTab.id, {
          type: 'STOP_SELECTION_MODE'
        })
      }

      setIsInSelectionMode(false)
      console.log('已退出交互式选择模式')
    } catch (err) {
      console.error('退出选择模式失败:', err)
      setIsInSelectionMode(false)
    }
  }

  /**
   * 获取友好的错误提示信息
   */
  const getFriendlyErrorMessage = (error: any, activeTab?: any): { title: string; message: string; suggestions: string[] } => {
    const errorMessage = error instanceof Error ? error.message : String(error)
    
    // 检查是否没有活动标签页
    if (!activeTab || !activeTab.id) {
      return {
        title: '无法获取当前标签页',
        message: '扩展无法访问当前标签页，可能是因为：',
        suggestions: [
          '当前没有打开任何网页',
          '当前页面是浏览器内置页面（如新标签页、设置页等）',
          '当前页面是扩展程序页面或Chrome内部页面',
          '请打开一个普通网页后重试'
        ]
      }
    }
    
    // 检查是否是Chrome内部页面
    if (activeTab.url && (activeTab.url.startsWith('chrome://') || activeTab.url.startsWith('chrome-extension://') || activeTab.url.startsWith('edge://') || activeTab.url.startsWith('about:'))) {
      return {
        title: '不支持的页面类型',
        message: '当前页面是浏览器系统页面，无法进行剪藏：',
        suggestions: [
          '浏览器内置页面（chrome://）不支持内容提取',
          '扩展程序页面不支持剪藏功能',
          '请切换到普通网页进行剪藏',
          '确保页面已完全加载后再进行剪藏'
        ]
      }
    }
    
    // 检查是否是file://协议
    if (activeTab.url && activeTab.url.startsWith('file://')) {
      return {
        title: '本地文件页面',
        message: '当前页面是本地文件，剪藏功能受限：',
        suggestions: [
          '本地HTML文件可能无法正常注入内容脚本',
          '建议将文件上传到web服务器后再剪藏',
          '或者使用浏览器的"打印为PDF"功能保存内容'
        ]
      }
    }
    
    // 通用错误
    return {
      title: '剪藏失败',
      message: '页面内容提取失败，可能的原因：',
      suggestions: [
        '页面结构过于复杂或使用了特殊技术',
        '网络连接不稳定，请检查网络状态',
        '页面还在加载中，请等待页面完全加载',
        '尝试刷新页面后重新剪藏',
        '如果是AI剪藏，请检查AI服务配置'
      ]
    }
  }

  /**
   * 从当前活动标签页提取内容（快捷剪藏，不使用AI）
   */
  const extractCurrentPageData = async () => {
    setLoading(true)
    setError(null)
    
    // 清除之前的完成状态提示
    setUploadedImages([])
    setNotionSyncResult(null)
    
    // 设置任务进度
    setCurrentTask({
      type: 'download',
      status: 'running',
      title: '快捷剪藏',
      description: '正在提取页面内容...',
      progress: 50
    })
    setShowTaskProgress(true)
    
    try {
      // 获取当前活动标签页
      const [activeTab] = await browser.tabs.query({ 
        active: true, 
        currentWindow: true 
      })

      if (!activeTab || !activeTab.id) {
        const errorInfo = getFriendlyErrorMessage(new Error('无法获取当前标签页'))
        throw new Error(JSON.stringify(errorInfo))
      }

      // 向content script发送消息，请求提取页面数据（不传递AI配置，使用传统算法）
      const response = await browser.tabs.sendMessage(activeTab.id, {
        type: 'EXTRACT_PAGE_DATA'
        // 不传递aiConfig，使用传统提取算法
      })

      if (response) {
        // 检查是否有错误
        if (response.error) {
          throw new Error(response.error)
        }
        
        setEditedTitle(response.title)
        setEditedTags(response.tags || 'clippings')
        
        // 处理规则应用
        const processedContent = config.rules?.removeEmojiImages ? removeEmojiImages(response.content) : response.content
        setEditedContent(processedContent)
        // 更新clipData中的content
        const updatedClipData = { ...response, content: processedContent }
        await handleClipSuccess(updatedClipData)
        console.log('成功提取页面数据（快捷剪藏）:', updatedClipData)
        
        // 更新任务状态为成功
        setCurrentTask({
          type: 'download',
          status: 'success',
          title: '快捷剪藏完成',
          description: '页面内容提取成功',
          progress: 100
        })
        
        // 3秒后隐藏任务进度
        setTimeout(() => {
          setShowTaskProgress(false)
          setCurrentTask(null)
        }, 3000)
      } else {
        const errorInfo = getFriendlyErrorMessage(new Error('未收到内容脚本的响应'), activeTab)
        throw new Error(JSON.stringify(errorInfo))
      }
    } catch (err) {
      console.error('提取页面数据失败:', err)
      
      // 尝试解析友好错误信息
      let errorInfo
      try {
        errorInfo = JSON.parse(err instanceof Error ? err.message : String(err))
      } catch {
        // 如果不是JSON格式，使用通用错误处理
        errorInfo = getFriendlyErrorMessage(err)
      }
      
      setError(errorInfo)
      
      // 更新任务状态为错误
      setCurrentTask({
        type: 'download',
        status: 'error',
        title: '快捷剪藏失败',
        description: '页面内容提取失败'
      })
    } finally {
      setLoading(false)
    }
  }

  /**
   * 使用AI辅助从当前活动标签页提取内容
   */
  const extractCurrentPageDataWithAI = async () => {
    setAiLoading(true)
    setError(null)
    
    // 清除之前的完成状态提示
    setUploadedImages([])
    setNotionSyncResult(null)
    
    // 设置任务进度
    setCurrentTask({
      type: 'ai',
      status: 'running',
      title: 'AI辅助剪藏',
      description: '正在使用AI分析页面结构...',
      progress: 30
    })
    setShowTaskProgress(true)
    
    try {
      // 获取当前活动标签页
      const [activeTab] = await browser.tabs.query({ 
        active: true, 
        currentWindow: true 
      })

      if (!activeTab || !activeTab.id) {
        const errorInfo = getFriendlyErrorMessage(new Error('无法获取当前标签页'))
        throw new Error(JSON.stringify(errorInfo))
      }

      // 向content script发送消息，请求提取页面数据（传递AI配置）
      const response = await browser.tabs.sendMessage(activeTab.id, {
        type: 'EXTRACT_PAGE_DATA',
        aiConfig: config.ai // 传递AI配置，优先使用AI分析
      })

      if (response) {
        // 检查是否有错误
        if (response.error) {
          throw new Error(response.error)
        }
        
        setEditedTitle(response.title)
        setEditedTags(response.tags || 'clippings')
        
        // 处理规则应用
        const processedContent = config.rules?.removeEmojiImages ? removeEmojiImages(response.content) : response.content
        const updatedClipData = { ...response, content: processedContent }
        await handleClipSuccess(updatedClipData)
        setEditedTitle(response.title)
        setEditedContent(processedContent)
        console.log('成功提取页面数据（AI剪藏）:', updatedClipData)
        
        // 如果使用了AI提取，显示相关信息
        if (response.aiAnalysis) {
          console.log('🤖 AI辅助提取信息:', response.aiAnalysis)
        }
        
        // 更新任务状态为成功
        setCurrentTask({
          type: 'ai',
          status: 'success',
          title: 'AI剪藏完成',
          description: '内容分析完成',
          progress: 100
        })
        
        // 3秒后隐藏任务进度
        setTimeout(() => {
          setShowTaskProgress(false)
          setCurrentTask(null)
        }, 3000)
      } else {
        const errorInfo = getFriendlyErrorMessage(new Error('未收到内容脚本的响应'), activeTab)
        throw new Error(JSON.stringify(errorInfo))
      }
    } catch (err) {
      console.error('AI提取页面数据失败:', err)
      
      // 尝试解析友好错误信息
      let errorInfo
      try {
        errorInfo = JSON.parse(err instanceof Error ? err.message : String(err))
      } catch {
        // 如果不是JSON格式，使用通用错误处理
        errorInfo = getFriendlyErrorMessage(err)
      }
      
      setError(errorInfo)
      
      // 更新任务状态为错误
      setCurrentTask({
        type: 'ai',
        status: 'error',
        title: 'AI剪藏失败',
        description: 'AI分析失败'
      })
    } finally {
      setAiLoading(false)
    }
  }

  /**
   * 移除表情符号图片的函数
   */
  const removeEmojiImages = (content: string): string => {
    if (!content) return content
    
    // 移除表情符号图片的正则表达式
    const emojiImagePattern = /!\[[^\]]*\]\([^)]*(?:emoji|emotion|face|wx\.qlogo|mmbiz\.qpic)[^)]*\)/gi
    return content.replace(emojiImagePattern, '')
  }

  /**
   * 同步到Notion
   */
  const syncToNotion = async () => {
    if (!clipData || !config.export.notion.enabled) {
      setError('Notion服务未启用，请先在设置中配置Notion服务')
      return
    }

    setIsSyncingToNotion(true)
    setSyncNotionStatus('正在连接Notion...')
    setNotionSyncResult(null)

    try {
      console.log('🔗 开始同步到Notion')
      console.log(`📋 剪藏数据: ${clipData.title}`)
      console.log(`⚙️ Notion配置: token=${config.export.notion.token ? '已配置' : '未配置'}, database=${config.export.notion.databaseId}`)

      setSyncNotionStatus('正在创建Notion页面...')

      // 动态导入Notion服务模块
      const { createExtensionNotionClient } = await import('@/lib/notion-api-client')
      const notionClient = createExtensionNotionClient(config.export.notion)

      // 准备同步数据
      const notionData: ClipDataForNotion = {
        title: clipData.title,
        url: clipData.url,
        author: clipData.author,
        publishDate: clipData.publishDate,
        site: clipData.site,
        tags: clipData.tags,
        content: clipData.content,
        coverImage: clipData.coverImage,
        timestamp: clipData.timestamp
      }

      console.log('📤 准备发送到Notion的数据:', notionData)

      // 创建Notion页面
      const result = await notionClient.createPage(notionData)
      
      if (result.success) {
        console.log('✅ Notion同步成功:', result)
        setSyncNotionStatus('同步完成！')
        setNotionSyncResult({
          pageId: result.pageId,
          pageUrl: result.pageUrl
        })
        
        // 3秒后清除状态
        setTimeout(() => {
          setIsSyncingToNotion(false)
          setSyncNotionStatus('')
        }, 3000)
      } else {
        throw new Error(result.error || 'Notion同步失败')
      }
    } catch (error) {
      console.error('❌ Notion同步失败:', error)
      setSyncNotionStatus(`同步失败: ${error instanceof Error ? error.message : '未知错误'}`)
      setTimeout(() => {
        setIsSyncingToNotion(false)
        setSyncNotionStatus('')
      }, 3000)
    }
  }

  /**
   * 上传所有图片
   */
  const uploadAllImages = async () => {
    if (!clipData || !config.image.enabled) {
      setError('图床服务未启用，请先在设置中配置图床服务')
      return
    }

    // 设置任务进度
    setCurrentTask({
      type: 'upload',
      status: 'running',
      title: '上传图片',
      description: '正在上传图片到云存储...',
      progress: 0
    })
    setShowTaskProgress(true)

    try {
      // 动态导入图片上传模块
      const imageUploaderModule = await import('@/lib/image-uploader')
      
      const { ImageUploadManager, ImageLinkReplacer } = imageUploaderModule
      const uploader = new ImageUploadManager(config.image)
      const replacer = new ImageLinkReplacer()

      // 提取Markdown中的图片链接
      const imageUrls = replacer.extractImageUrls(clipData.content)
      
      if (imageUrls.length === 0) {
        setCurrentTask({
          type: 'upload',
          status: 'success',
          title: '上传完成',
          description: '没有找到需要上传的图片',
          progress: 100
        })
        
        setTimeout(() => {
          setShowTaskProgress(false)
          setCurrentTask(null)
        }, 2000)
        return
      }

      // 批量上传图片
      const uploadResults = []
      for (let i = 0; i < imageUrls.length; i++) {
        const imageUrl = imageUrls[i]
        const progress = ((i + 1) / imageUrls.length) * 100
        
        setCurrentTask({
          type: 'upload',
          status: 'running',
          title: '上传图片',
          description: `正在上传第 ${i + 1}/${imageUrls.length} 张图片...`,
          progress
        })

        try {
          const result = await uploader.uploadFromUrl(imageUrl)
          if (result.success) {
            uploadResults.push({ original: imageUrl, uploaded: result.url! })
          }
        } catch (error) {
          console.error(`上传图片失败: ${imageUrl}`, error)
        }
      }

      // 替换Markdown中的图片链接
      let updatedContent = clipData.content
      for (const result of uploadResults) {
        updatedContent = replacer.replaceImageUrl(updatedContent, result.original, result.uploaded)
      }

      // 更新clipData
      const updatedClipData = { ...clipData, content: updatedContent }
      setClipData(updatedClipData)
      setEditedContent(updatedContent)
      setUploadedImages(uploadResults)

      setCurrentTask({
        type: 'upload',
        status: 'success',
        title: '上传完成',
        description: `成功上传 ${uploadResults.length}/${imageUrls.length} 张图片`,
        progress: 100,
        result: {
          uploadedCount: uploadResults.length,
          totalCount: imageUrls.length
        }
      })

      // 3秒后隐藏任务进度
      setTimeout(() => {
        setShowTaskProgress(false)
        setCurrentTask(null)
      }, 3000)
    } catch (error) {
      console.error('批量上传图片失败:', error)
      setCurrentTask({
        type: 'upload',
        status: 'error',
        title: '上传失败',
        description: `图片上传失败: ${error instanceof Error ? error.message : '未知错误'}`
      })
    }
  }

  /**
   * 下载为Markdown文件
   */
  const downloadAsMarkdown = () => {
    if (!clipData) return

    const content = `# ${clipData.title}

**来源**: [${clipData.site}](${clipData.url})
**时间**: ${clipData.timestamp}
${clipData.author ? `**作者**: ${clipData.author}` : ''}
${clipData.tags ? `**标签**: ${clipData.tags}` : ''}

---

${clipData.content}

---
*由 mpclipper 生成*`

    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${clipData.title}.md`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // 辅助函数
  const handleGoToSettings = () => {
    // 通过消息通知父组件切换到设置页
    const event = new CustomEvent('switchToSettings')
    document.dispatchEvent(event)
  }

  const handleTitleChange = (newTitle: string) => {
    setEditedTitle(newTitle)
    if (clipData) {
      setClipData({ ...clipData, title: newTitle })
    }
  }

  const handleContentChange = (newContent: string) => {
    setEditedContent(newContent)
    if (clipData) {
      setClipData({ ...clipData, content: newContent })
    }
  }

  const handleTagsChange = (newTags: string[]) => {
    const tagsString = newTags.join(',')
    setEditedTags(tagsString)
    if (clipData) {
      setClipData({ ...clipData, tags: tagsString })
    }
  }

  const dismissTask = () => {
    setShowTaskProgress(false)
    setCurrentTask(null)
  }

  const retryCurrentTask = () => {
    if (currentTask?.type === 'ai') {
      extractCurrentPageDataWithAI()
    } else if (currentTask?.type === 'download') {
      extractCurrentPageData()
    } else if (currentTask?.type === 'upload') {
      uploadAllImages()
    } else if (currentTask?.type === 'notion') {
      syncToNotion()
    }
  }

  // 初始化页面模式
  useEffect(() => {
    if (!historyLoading) {
      const mode = getPageToShow()
      setPageMode(mode)
      
      // 如果是当前页面可剪藏，自动执行快捷剪藏
      if (mode === 'current' && currentPageInfo.canClip && !clipData && !loading && !aiLoading) {
        extractCurrentPageData()
      }
      // 如果是显示最新剪藏，加载最新数据
      else if (mode === 'latest') {
        const latestClip = getLatestClip()
        if (latestClip) {
          setClipData(latestClip)
          setEditedTitle(latestClip.title)
          setEditedContent(latestClip.content)
          setEditedTags(latestClip.tags || 'clippings')
        }
      }
    }
  }, [historyLoading, getPageToShow, getLatestClip, currentPageInfo.canClip, clipData, loading, aiLoading])

  // 监听页面变化，重新检查当前页面
  useEffect(() => {
    const interval = setInterval(() => {
      checkCurrentPage()
    }, 2000) // 每2秒检查一次

    return () => clearInterval(interval)
  }, [checkCurrentPage])

  // 监听页面URL变化，自动重新剪藏
  useEffect(() => {
    if (currentPageInfo.url && currentPageInfo.canClip) {
      // 如果当前剪藏的URL和页面URL不同，且新页面可剪藏，则重新剪藏
      if (clipData && clipData.url !== currentPageInfo.url) {
        // 清空当前数据，准备重新剪藏
        setClipData(null)
        setError(null)
        // 延迟执行剪藏，避免频繁调用
        const timer = setTimeout(() => {
          if (!loading && !aiLoading) {
            extractCurrentPageData()
          }
        }, 1000)
        return () => clearTimeout(timer)
      }
    }
  }, [currentPageInfo.url, currentPageInfo.canClip, clipData?.url, loading, aiLoading])

  // 当剪藏成功后，保存到历史记录
  const handleClipSuccess = async (newClipData: ClipData) => {
    setClipData(newClipData)
    await addClipToHistory(newClipData)
    setPageMode('current')
  }

  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // 如果正在编辑文本，跳过快捷键
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return
      }

      // Alt + 快捷键组合
      if (event.altKey && !event.ctrlKey && !event.metaKey) {
        switch (event.key.toLowerCase()) {
          case 'e':
            event.preventDefault()
            if (!loading && !aiLoading) {
              extractCurrentPageData()
            }
            break
          case 'a':
            event.preventDefault()
            if (!loading && !aiLoading && config.ai.enabled) {
              extractCurrentPageDataWithAI()
            }
            break
          case 's':
            event.preventDefault()
            if (!loading && !aiLoading) {
              startSelectionMode()
            }
            break
          case 'n':
            event.preventDefault()
            if (clipData && config.export.notion.enabled) {
              syncToNotion()
            }
            break
        }
      }

      // ESC 退出选择模式
      if (event.key === 'Escape' && isInSelectionMode) {
        event.preventDefault()
        exitSelectionMode()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [loading, aiLoading, isInSelectionMode, clipData, config.ai.enabled, config.export.notion.enabled])

  return (
    <TooltipProvider>
      <div className="h-full flex flex-col relative w-full" data-clip-root>
        {/* 选择模式指示器 */}
        {isInSelectionMode && (
          <div className="flex-shrink-0">
            <SelectionModeIndicator onExitSelection={exitSelectionMode} />
          </div>
        )}

        {/* 错误提示 */}
        {error && (
          <div className="flex-shrink-0 p-4">
            <ErrorCard
              title={typeof error === 'string' ? '操作失败' : error.title}
              message={typeof error === 'string' ? error : error.message}
              suggestions={typeof error === 'string' ? [] : error.suggestions}
              onRetry={retryCurrentTask}
              onGoToSettings={handleGoToSettings}
              canRetry={!!currentTask}
            />
          </div>
        )}

        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col w-full min-w-0">
          {historyLoading ? (
            /* 加载中 */
            <div className="flex-1 flex items-center justify-center">
              <div className="flex items-center gap-3">
                <Loader2 className="h-5 w-5 animate-spin" />
                <span className="text-muted-foreground">正在加载...</span>
              </div>
            </div>
          ) : pageMode === 'welcome' ? (
            /* 欢迎页 */
            <WelcomePage onGoToSettings={handleGoToSettings} />
          ) : (
            <>
              {/* 顶部工具栏 */}
              {!isInSelectionMode && (
                <div className="flex-shrink-0">
                  <TopToolbar
                    onQuickExtract={extractCurrentPageData}
                    onAIExtract={extractCurrentPageDataWithAI}
                    onImageUpload={uploadAllImages}
                    onSelectionMode={startSelectionMode}
                    onGoToSettings={handleGoToSettings}
                    extracting={loading}
                    aiExtracting={aiLoading}
                    uploading={isUploadingImages}
                    inSelectionMode={isInSelectionMode}
                    aiEnabled={config.ai.enabled}
                    imageEnabled={config.image.enabled}
                    pageMode={pageMode}
                    currentPageInfo={currentPageInfo}
                  />
                </div>
              )}

              {/* 可滚动内容区 */}
              <div className="flex-1 w-full">
                {!clipData && !loading && !aiLoading ? (
                  /* 准备剪藏状态 */
                  <div className="flex-1 flex items-center justify-center">
                    <div className="text-center space-y-4">
                      <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                        <Loader2 className="h-6 w-6 text-primary animate-spin" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-foreground">准备剪藏</p>
                        <p className="text-xs text-muted-foreground">正在初始化页面内容...</p>
                      </div>
                    </div>
                  </div>
                ) : (
                  <ScrollArea className="h-full w-full">
                    <div className="space-y-0 w-full max-w-full min-w-0">
                      {/* 可编辑标题 */}
                      {clipData && (
                        <EditableTitle
                          title={clipData.title}
                          onTitleChange={handleTitleChange}
                          placeholder="点击编辑标题..."
                        />
                      )}

                      {/* 属性区域 */}
                      {clipData && (
                        <PropertiesSection
                          title={clipData.title}
                          source={clipData.url}
                          author={clipData.author}
                          publishDate={clipData.publishDate}
                          created={clipData.timestamp}
                          tags={clipData.tags ? clipData.tags.split(',').filter(Boolean) : []}
                          category="Clippings"
                          isRead={isRead}
                          onIsReadChange={setIsRead}
                          onTagsChange={handleTagsChange}
                        />
                      )}

                      {/* 正文编辑器 */}
                      {clipData && (
                        <ContentEditor
                          content={clipData.content}
                          onContentChange={handleContentChange}
                          defaultExpanded={true}
                        />
                      )}
                    </div>
                  </ScrollArea>
                )}
              </div>

              {/* 底部导出区域 */}
              {clipData && (
                <div className="flex-shrink-0">
                  <ExportSection
                    onLocalSave={downloadAsMarkdown}
                    onSyncNotion={syncToNotion}
                    notionEnabled={config.export.notion.enabled}
                    syncing={isSyncingToNotion}
                    onGoToSettings={handleGoToSettings}
                    hasContent={!!clipData}
                  />
                </div>
              )}
            </>
          )}
        </div>

        {/* 任务进度条 */}
        {showTaskProgress && currentTask && (
          <div className="absolute bottom-0 left-0 right-0 z-50">
            <TaskProgress
              type={currentTask.type}
              status={currentTask.status}
              title={currentTask.title}
              description={currentTask.description}
              progress={currentTask.progress}
              result={currentTask.result}
              onCancel={currentTask.status === 'running' ? dismissTask : undefined}
              onRetry={currentTask.status === 'error' ? retryCurrentTask : undefined}
              onDismiss={currentTask.status !== 'running' ? dismissTask : undefined}
            />
          </div>
        )}

        {/* 采集进行时的禁用遮罩 */}
        {(loading || aiLoading) && (
          <div className="absolute inset-0 bg-background/50 backdrop-blur-sm z-40 flex items-center justify-center">
            <div className="bg-background border rounded-lg p-4 shadow-lg">
              <div className="flex items-center gap-3">
                <Loader2 className="h-5 w-5 animate-spin" />
                <div>
                  <p className="font-medium">
                    {loading && "正在提取页面内容..."}
                    {aiLoading && "AI 正在分析页面结构..."}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    请稍候，避免进行其他操作
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </TooltipProvider>
  )
}
