# mpclipper —— 网页剪藏浏览器扩展（Sidepanel）

一个基于 WXT + React + TypeScript + Tailwind CSS + shadcn/ui 的现代浏览器扩展，用于在侧边栏中完成网页内容的智能剪藏、图片处理与 Notion 同步。

演示见 `public/demo.gif`。

---

## ✨ 功能一览

- 一键剪藏：从当前页面快速提取正文为 Markdown/HTML
- AI 辅助：可选接入 OpenAI/Claude/DeepSeek/Kimi/OpenRouter 等，智能定位正文区域
- 交互选择：在页面上框选一个或多个元素进行精确剪藏
- 富媒体支持：自定义 Turndown 规则，增强 `video`/`iframe` 等标签的 Markdown 表达
- 图片处理：多策略下载（含 referer、background 跨域代理、no-cors 回退）、预加载缓存
- 图床上传：内置上传管理器，现支持 S3；AliOSS/七牛建议走 S3 兼容端点
- Notion 同步：通过 background 代理调用 Notion API，支持 Markdown → Notion Blocks 转换
- SPA 监听：对 X/Twitter 等路由切换做了去抖与内容就绪检测
- 侧边栏 UI：剪藏与设置两大页签，持久化主题与配置

---

## 🧱 架构总览

扩展基于 WXT 的多入口结构：

- `entrypoints/background.ts`
  - 注册点击扩展图标自动打开侧边栏
  - 安装时初始化 sidepanel 选项
  - 接收并代理来自侧边栏的 Notion API 请求（消息 `NOTION_API_CALL`），规避 CORS/上下文限制
  - 处理图片下载代理（消息 `DOWNLOAD_IMAGE_VIA_BACKGROUND`），将结果以 base64 返回

- `entrypoints/content.ts`
  - 注入到所有页面（`matches: ['<all_urls>']`）
  - 配置并扩展 Turndown，将 HTML 转换为 Markdown：
    - 自定义 `strikethrough`、`highlight`、`video`、`iframe` 等规则
  - 站点规则选择：少数派、小红书、推特（`lib/content/site-rules.ts`）
  - AI 辅助提取路径：`lib/ai-content-extractor.ts`
  - 通用启发式提取、元数据（作者、日期、封面、favicon）汇总为剪藏数据
  - SPA 路由变化监听：`lib/content/spa-watch.ts`，自动重提取并发送 `SELECTED_CONTENT`
  - 统一消息入口：`handleContentMessages`（EXTRACT_PAGE_DATA / START_SELECTION_MODE / DOWNLOAD_IMAGE / PRELOAD_IMAGES / GET_PRELOADED_IMAGE）

- `entrypoints/sidepanel/`
  - `App.tsx`：页签布局（剪藏 / 设置），主题切换
  - `ClipPage.tsx`：
    - 快捷剪藏与 AI 剪藏（向 content 发送 `EXTRACT_PAGE_DATA`）
    - 交互选择模式（向 content 发送 `START_SELECTION_MODE`）
    - 预览卡片（`components/ClipCard.tsx`），编辑标题/内容/标签
    - 图片生成功能（截图 DOM → 图片，复制/下载，见 `lib/image-generator.ts`）
    - 图片转存：`ImageUploadManager` + `ImageLinkReplacer`（S3 上传，替换 Markdown 中的图片链接）
    - Notion 同步：`lib/notion-api-client.ts`（background 代理），Markdown → Blocks 转换（`lib/markdown-to-notion.ts`）
  - `SettingsPage.tsx`：AI/图床/Notion/本地保存/规则 等配置与一键连通性检测

---

## 🗂 目录结构（关键目录）

```
entrypoints/
  background.ts          # 背景脚本：sidepanel 管理、Notion 代理、图片下载代理
  content.ts             # 内容脚本：提取、Turndown、SPA 监听、消息处理
  sidepanel/
    App.tsx              # 侧边栏应用入口
    ClipPage.tsx         # 剪藏页（预览、AI、上传、Notion）
    SettingsPage.tsx     # 设置页（AI/图床/Notion/本地/规则）
    index.html, main.tsx # 挂载入口

components/
  ClipCard.tsx           # 剪藏卡片预览
  ui/                    # shadcn/ui 组件

hooks/
  use-config.ts          # 用户配置持久化（storage），含 AI/图床/Notion/本地保存/规则
  use-settings.ts        # 主题和 UI 状态持久化
  use-theme.ts           # 跟随系统 / 明暗主题

lib/
  ai-content-extractor.ts   # AI 提取（多 Provider）、DOM 采样与提示词构造
  content/
    extract.ts              # 站点规则 → AI → 启发式 的提取主流程
    site-rules.ts           # 站点匹配与选择器快速提取
    selection.ts            # 交互选择覆盖层与事件
    image-download.ts       # 内容脚本侧图片下载、预加载、多 referer 策略
    spa-watch.ts            # SPA 路由监听与内容就绪检测
    turndown.ts             # 共享 turndown 实例（供模块使用）
  image-generator.ts        # DOM → 图片、复制/下载
  image-uploader.ts         # S3/AliOSS/七牛上传器与统一管理器
  markdown-to-notion.ts     # Markdown → Notion Blocks 转换器
  notion-api-client.ts      # background 代理调用 Notion API
  utils.ts                  # 通用工具

wxt.config.ts               # WXT 配置与 manifest（sidePanel / storage / activeTab 权限）
package.json                # 脚本与依赖
```

---

## 🔌 消息与数据流

- Content ↔ Sidepanel
  - `EXTRACT_PAGE_DATA`：侧边栏请求提取 → 内容脚本返回剪藏数据
  - `START_SELECTION_MODE`/`STOP_SELECTION_MODE`：开启/关闭交互选择
  - `SELECTED_CONTENT`：内容脚本在 SPA 或选择完成后主动推送数据给侧边栏
  - `DOWNLOAD_IMAGE`/`PRELOAD_IMAGES`/`GET_PRELOADED_IMAGE`：图片下载与预加载

- Sidepanel ↔ Background
  - `NOTION_API_CALL`：侧边栏委托背景脚本发起 Notion API 请求
  - `DOWNLOAD_IMAGE_VIA_BACKGROUND`：由背景脚本跨域下载图片并返回 base64

---

## ⚙️ 配置与能力

打开侧边栏的“设置”页签可视化配置：

- AI 配置
  - Provider：`openai` | `claude` | `deepseek` | `kimi` | `openrouter` | `custom`
  - Base URL：可自定义兼容端点
  - Model / Temperature / Max Tokens
  - 一键连通性测试

- 图床配置（图片转存）
  - Provider：`s3`（推荐） | `alioss` | `qiniu`
  - S3：支持自定义 endpoint、public-read、domain/path 拼接最终外链
  - 提示：AliOSS/七牛建议走 S3 兼容端点（直接前端上传原生接口需后端签名支持）

- 导出配置
  - Notion：Integration Token、Database ID、连通性测试
  - 本地保存：是否启用、保存目录（浏览器兼容 File System Access API 时可选目录）

- 规则
  - 移除表情符号图片（避免微信/QQ 表情干扰正文）

配置持久化存储在浏览器 `storage`，无需后端。

---

## 🛠 本地开发

前置要求：Node.js 18+，pnpm

安装依赖：

```bash
pnpm install
```

启动开发（默认浏览器/指定浏览器）：

```bash
pnpm dev
pnpm dev:chrome
pnpm dev:firefox
pnpm dev:edge
pnpm dev:safari
```

构建与打包：

```bash
pnpm build
pnpm zip
```

说明：WXT 会在 `.output/` 生成构建产物与各浏览器的压缩包，可在对应浏览器的“扩展开发者模式”中加载。

---

## 🔍 提取策略说明

1. 站点规则（优先）：如 少数派/小红书/推特，命中即用选择器或专用解析
2. AI 辅助（可选）：采样 DOM 结构 → 构造提示词 → 多 Provider 请求 → 返回候选选择器并验证
3. 启发式回退：按语义/密度在 `article/main/body` 中清洗提取

Turndown 进行了增强，`video`/`iframe` 将被转换为带链接与上下文的 Markdown 富信息块；并尽量保留图片、链接与标题语义。

---

## 🖼 图片下载与上传

- 下载侧（内容脚本）：
  - 多策略尝试：带 referer、no-referrer、no-cors、代理、`<img>` 绘制 canvas 回退
  - 站点专门化：`sinaimg.cn`（微博）、`zhimg.com`（知乎）、`xiaohongshu.com`（小红书）等
  - 预加载缓存：短时缓存 base64，避免重复请求
- 背景代理下载：在部分站点通过 background 绕过上下文限制
- 上传侧（ImageUploadManager）：
  - S3 完整实现（可选自定义 endpoint/domain/path）
  - AliOSS/七牛：建议走 S3 兼容端点（当前直接前端上传需后端签名，默认抛出提示）

---

## 🗒 Notion 同步

- `ExtensionNotionClient` 在侧边栏内通过消息让 background 代表发起 Notion API 请求
- 创建页面时：
  - Properties：Title、URL、Author、Published、Source、Clipped、Tags、Cover
  - Content：先插入封面/元信息/原文链接/分割线，再将 Markdown 转为 Blocks（遇到上限分批追加）

---

## 🔐 权限与清单（manifest）

`wxt.config.ts` 中声明：

- `permissions`: `sidePanel`, `storage`, `activeTab`
- `host_permissions`: `https://api.notion.com/*`, `<all_urls>`
- `side_panel.default_path`: `sidepanel.html`

注意：令牌等敏感信息仅存储在本地浏览器 `storage`，请妥善保管。

---

## 🧩 技术栈

- WXT、React 19、TypeScript 5、Tailwind CSS 4、shadcn/ui
- Turndown、react-markdown、remark-gfm、rehype-raw
- AWS SDK v3（S3）、html2canvas、file-saver

---

## 🚦 已知限制与建议

- AliOSS/七牛直传：前端直传需要后端签名，建议使用 S3 兼容端点或后端代理
- 某些站点使用严格 CSP/反爬策略时，图片/正文可能需要 background/代理/AI 辅助回退
- Safari 开发体验与权限与 Chromium/Firefox 存在差异，建议优先在 Chromium 测试

---

## 📌 常用脚本

`package.json`：

- 开发：`pnpm dev` / `pnpm dev:chrome|firefox|edge|safari`
- 构建：`pnpm build` / `pnpm build:chrome|firefox|edge|safari`
- 打包：`pnpm zip` / `pnpm zip:chrome|firefox|edge|safari`

---

## 📎 进一步规划

- 更多站点内置规则与元素清洗
- AI 结构化提取（标题/作者/时间/标签更稳健）
- 图床多提供商直传（配合后端签名）
- 多平台同步（Notion 之外，如 Obsidian/飞书/Confluence 等）

---

如需查看英文版，可在后续补充 `README.en.md`；中文模板版参考 `README.zh-CN.md`。


