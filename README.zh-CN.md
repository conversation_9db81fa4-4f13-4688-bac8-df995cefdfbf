# 浏览器侧边栏扩展程序模板

这是一个使用 [WXT](https://wxt.dev/) + [React](https://react.dev/) + [TypeScript](https://www.typescriptlang.org/) + [Tailwind CSS](https://tailwindcss.com/) 构建的浏览器侧边栏（Sidepanel）扩展程序模板。

旨在帮助开发者快速启动一个现代化的、功能强大的浏览器扩展项目。

![演示动画](public/demo.gif)

---

## ✨ 主要特性

- **⚡️ 下一代构建工具**: 使用 [WXT](https://wxt.dev/)，享受极速的现代浏览器扩展开发体验。
- **⚛️ 现代 UI 框架**: 基于 [React](https://react.dev/) 和 [TypeScript](https://www.typescriptlang.org/)，代码健壮且易于维护。
- **💅 优雅的 UI 组件**: 集成 [shadcn/ui](https://ui.shadcn.com/)，可通过 JSON 文件轻松定制和扩展。
- **🎨 响应式设计**: 完美适配移动端和桌面端视图。
- **🌗 明暗主题**: 内置亮色和暗色两种主题模式。
- **🧩 遵循最佳实践**: 提供一个组织良好、可扩展且遵循社区最佳实践的项目结构。

## 🛠️ 技术栈

- **框架**: [WXT](https://wxt.dev/)
- **UI 库**: [React](https://react.dev/)
- **语言**: [TypeScript](https://www.typescriptlang.org/)
- **样式**: [Tailwind CSS](https://tailwindcss.com/)
- **UI 组件**: [shadcn/ui](https://ui.shadcn.com/)
- **包管理器**: [pnpm](https://pnpm.io/)

---

## 📂 项目目录结构

```
.
├── entrypoints/      # 扩展程序入口点
│   ├── background.ts # 后台服务脚本
│   ├── content.ts    # 内容脚本，注入到网页
│   └── sidepanel/    # 侧边栏 UI 界面
│       ├── App.tsx
│       └── main.tsx
├── components/       # 可复用的 React 组件
│   └── ui/           # shadcn/ui 组件
├── hooks/            # 自定义 React Hooks
├── lib/              # 通用工具函数
├── public/           # 静态资源 (如图标)
├── wxt.config.ts     # WXT 配置文件
└── package.json      # 项目依赖和脚本
```

### 关键目录解释

-   `entrypoints/`: WXT 框架的核心，定义了扩展的所有入口。
    -   `sidepanel/`: **侧边栏界面的主要开发目录**。你将在这里构建应用的核心 UI 和功能。
    -   `background.ts`: 扩展的后台服务。用于处理浏览器事件、管理状态等，没有 UI。
    -   `content.ts`: 内容脚本。可以注入到普通网页中，用于与页面 DOM 交互。
-   `components/ui/`: `shadcn/ui` 的组件存放目录。这些是构成界面的基础元素。
-   `hooks/`: 存放自定义的 React Hooks，用于封装和复用组件逻辑。
-   `public/`: 存放不需要编译的静态文件，如扩展程序的图标。

---

## 🚀 快速上手

请按照以下步骤在本地运行此项目。

### 1. 先决条件

- [Node.js](https://nodejs.org/) (版本 >= 18.0.0)
- [pnpm](https://pnpm.io/installation)

### 2. 安装依赖

克隆项目到本地后，在项目根目录执行以下命令安装所有依赖项：

```bash
pnpm install
```

### 3. 启动开发模式

执行以下命令来启动 WXT 开发服务器：

```bash
pnpm dev
```

该命令会启动一个已加载此扩展程序的浏览器实例。代码的任何改动都会被实时监听，并自动热重载（HMR）。

### 4. 查看侧边栏

1.  在 `pnpm dev` 打开的新浏览器窗口中，点击浏览器工具栏右侧的 **侧边栏图标**。
2.  在下拉菜单中，选择 **Sidepanel Extension Template** 以显示侧边栏界面。

---

## 📦 可用脚本

在 `package.json` 中定义了几个有用的脚本：

-   `pnpm dev`: 启动开发服务器，并打开一个带扩展的浏览器实例。
-   `pnpm build`: 为生产环境打包和压缩扩展程序。
-   `pnpm postinstall`: 安装后自动运行 `wxt prepare`，用于生成必要的类型定义。
-   `pnpm zip`: 将 `build` 后的产物打包成 `.zip` 文件，方便分发或上传到应用商店。
-   `pnpm clean`: 清理 WXT 生成的构建产物和临时文件。 