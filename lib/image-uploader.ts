/**
 * 图床上传服务
 * 支持多种图床服务商
 */

import { S3Client, PutObjectCommand, PutObjectCommandInput, HeadBucketCommand } from '@aws-sdk/client-s3'
import { ImageConfig } from '@/hooks/use-config'
import pLimit from 'p-limit'
import pRetry from 'p-retry'

// 日志级别配置
const LOG_LEVEL = (globalThis as any).__IMAGE_UPLOADER_LOG_LEVEL__ || 'INFO'
const DEBUG_MODE = LOG_LEVEL === 'DEBUG'

// 安全日志函数
const safeLog = {
  debug: (...args: any[]) => {
    if (DEBUG_MODE) console.log('[DEBUG]', ...args)
  },
  info: (...args: any[]) => {
    console.log('[INFO]', ...args)
  },
  warn: (...args: any[]) => {
    console.warn('[WARN]', ...args)
  },
  error: (...args: any[]) => {
    console.error('[ERROR]', ...args)
  }
}

// 脱敏函数
const sanitizeConfig = (config: any) => ({
  ...config,
  accessKey: config.accessKey ? `${config.accessKey.substring(0, 4)}***` : undefined,
  secretKey: config.secretKey ? '***' : undefined
})

const sanitizeUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    return `${urlObj.protocol}//${urlObj.hostname}${urlObj.pathname.length > 50 ? urlObj.pathname.substring(0, 50) + '...' : urlObj.pathname}`
  } catch {
    return url.length > 50 ? url.substring(0, 50) + '...' : url
  }
}

export interface UploadResult {
  success: boolean
  url?: string
  error?: string
  provider: string
  originalUrl: string
  httpStatus?: number
  retryCount?: number
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

/**
 * 图床上传器基类
 */
abstract class ImageUploader {
  protected config: ImageConfig

  constructor(config: ImageConfig) {
    this.config = config
  }

  abstract upload(
    blob: Blob, 
    filename: string, 
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult>

  abstract testConnection(): Promise<{ success: boolean; error?: string }>

  /**
   * 生成文件名
   */
  protected generateFilename(originalFilename?: string): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8)
    const extension = originalFilename?.split('.').pop() || 'jpg'
    return `${timestamp}_${random}.${extension}`
  }

  /**
   * 验证配置
   */
  protected validateConfig(): boolean {
    return this.config.enabled
  }
}

/**
 * Amazon S3上传器
 */
class S3Uploader extends ImageUploader {
  private client: S3Client | null = null

  private initializeClient() {
    if (!this.config.accessKey || !this.config.secretKey || !this.config.region) {
      throw new Error('S3配置不完整：需要Access Key、Secret Key和Region')
    }

    const clientConfig: any = {
      region: this.config.region,
      credentials: {
        accessKeyId: this.config.accessKey,
        secretAccessKey: this.config.secretKey
      }
    }

    // 如果配置了自定义endpoint，使用自定义endpoint（适用于兼容S3的服务）
    if (this.config.endpoint) {
      clientConfig.endpoint = this.config.endpoint
      clientConfig.forcePathStyle = true // 强制使用路径样式，适用于自建S3兼容服务
    }

    this.client = new S3Client(clientConfig)
  }

  async upload(
    blob: Blob,
    filename: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    const originalUrl = 'unknown'
    safeLog.info('🔄 S3上传开始')
    safeLog.debug('📁 文件信息:', {
      filename,
      size: blob.size,
      type: blob.type
    })
    
    try {
      if (!this.validateConfig()) {
        const error = 'S3配置无效'
        safeLog.error('❌ S3配置验证失败')
        throw new Error(error)
      }

      if (!this.config.bucket) {
        const error = 'S3 Bucket名称未配置'
        safeLog.error('❌ S3 Bucket验证失败')
        throw new Error(error)
      }

      safeLog.debug('🔧 初始化S3客户端...')
      this.initializeClient()
      if (!this.client) {
        const error = 'S3客户端初始化失败'
        safeLog.error('❌ S3客户端初始化失败')
        throw new Error(error)
      }

      const generatedFilename = this.generateFilename(filename)
      // 规范化路径：确保以 / 结尾，并对 Key 进行编码
      const normalizedPath = this.config.path ? 
        (this.config.path.endsWith('/') ? this.config.path : `${this.config.path}/`) : ''
      const key = `${normalizedPath}${generatedFilename}`
      // 对 Key 进行 URI 编码（分段编码，保留路径分隔符）
      const encodedKey = key.split('/').map(segment => encodeURIComponent(segment)).join('/')
      
      safeLog.debug('📝 上传参数:', {
        bucket: this.config.bucket,
        key: sanitizeUrl(key),
        filename: generatedFilename,
        size: blob.size
      })

      // 模拟进度
      if (onProgress) {
        onProgress({ loaded: 0, total: 100, percentage: 0 })
      }

      // 准备上传参数 - 将Blob转换为ArrayBuffer避免readableStream错误
      const arrayBuffer = await blob.arrayBuffer()
      const uploadParams: PutObjectCommandInput = {
        Bucket: this.config.bucket,
        Key: encodedKey,
        Body: new Uint8Array(arrayBuffer),
        ContentType: blob.type || 'image/jpeg',
        // 设置公共读取权限（如果S3服务支持）
        ACL: 'public-read'
      }

      safeLog.debug('⬆️ 开始S3上传...')
      // 执行上传
      const command = new PutObjectCommand(uploadParams)
      const response = await this.client.send(command)
      
      safeLog.debug('✅ S3上传完成:', { ETag: response.ETag, VersionId: response.VersionId })

      if (onProgress) {
        onProgress({ loaded: 100, total: 100, percentage: 100 })
      }

      // 构建访问URL
      let url: string
      if (this.config.domain) {
        // 使用自定义域名
        url = `${this.config.domain.replace(/\/$/, '')}/${encodedKey}`
        console.log('🌐 使用自定义域名构建URL:', url)
      } else if (this.config.endpoint) {
        // 使用自定义endpoint，支持path-style和virtual-hosted-style
        const endpointUrl = new URL(this.config.endpoint)
        const forcePathStyle = this.config.forcePathStyle || false
        
        if (forcePathStyle) {
          // Path-style: https://endpoint/bucket/key
          url = `${endpointUrl.protocol}//${endpointUrl.host}/${this.config.bucket}/${encodedKey}`
          safeLog.debug('🔗 使用path-style构建URL')
        } else {
          // Virtual-hosted-style: https://bucket.endpoint/key
          url = `${endpointUrl.protocol}//${this.config.bucket}.${endpointUrl.host}/${encodedKey}`
          safeLog.debug('🔗 使用virtual-hosted-style构建URL')
        }
      } else {
        // 使用标准AWS S3 URL格式
        url = `https://${this.config.bucket}.s3.${this.config.region}.amazonaws.com/${encodedKey}`
        safeLog.debug('🏷️ 使用标准AWS S3 URL格式')
      }

      safeLog.info('✅ S3上传成功完成')
      safeLog.debug('📎 最终URL:', sanitizeUrl(url))

      return {
        success: true,
        url,
        provider: 's3',
        originalUrl: url
      }

    } catch (error) {
      safeLog.error('❌ S3上传失败:', error instanceof Error ? error.message : '上传失败')
      safeLog.debug('❌ 错误详情:', {
        name: error instanceof Error ? error.name : 'Unknown',
        message: error instanceof Error ? error.message : '上传失败'
      })
      
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        provider: 's3',
        originalUrl
      }
    }
  }

  /**
   * 测试图床连接
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.validateConfig()) {
        return { success: false, error: 'S3配置无效' }
      }

      if (!this.config.bucket) {
        return { success: false, error: 'S3 Bucket名称未配置' }
      }

      this.initializeClient()
      if (!this.client) {
        return { success: false, error: 'S3客户端初始化失败' }
      }

      // 测试连接：列出bucket（权限检查）
      const command = new HeadBucketCommand({ Bucket: this.config.bucket })
      await this.client.send(command)
      
      return { success: true }

    } catch (error: any) {
      let errorMessage = '连接测试失败'
      
      if (error.name === 'NoSuchBucket') {
        errorMessage = 'Bucket不存在'
      } else if (error.name === 'Forbidden' || error.name === 'AccessDenied') {
        errorMessage = '访问权限不足，请检查Access Key和Secret Key'
      } else if (error.name === 'InvalidAccessKeyId') {
        errorMessage = 'Access Key ID无效'
      } else if (error.name === 'SignatureDoesNotMatch') {
        errorMessage = 'Secret Access Key无效'
      } else if (error.message) {
        errorMessage = error.message
      }
      
      return { success: false, error: errorMessage }
    }
  }

  protected validateConfig(): boolean {
    return !!(this.config.enabled && 
             this.config.accessKey && 
             this.config.secretKey && 
             this.config.region && 
             this.config.bucket)
  }
}

/**
 * 阿里云OSS上传器
 */
class AliOSSUploader extends ImageUploader {
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.validateConfig()) {
        return { success: false, error: '阿里云OSS配置无效' }
      }

      if (!this.config.accessKey || !this.config.secretKey || !this.config.bucket || !this.config.region) {
        return { success: false, error: '阿里云OSS配置不完整' }
      }

      // 由于在浏览器环境中直接使用OSS SDK较复杂，建议通过后端API代理
      return { success: false, error: '阿里云OSS连接测试需要后端支持' }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败'
      }
    }
  }

  async upload(
    blob: Blob, 
    filename: string, 
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    console.log('🔄 AliOSSUploader.upload 开始')
    console.log('📁 阿里云OSS文件信息:', {
      filename,
      size: blob.size,
      type: blob.type
    })
    
    try {
      if (!this.validateConfig()) {
        const error = '阿里云OSS配置无效'
        console.error('❌ 阿里云OSS配置验证失败')
        throw new Error(error)
      }

      if (!this.config.accessKey || !this.config.secretKey || !this.config.bucket || !this.config.region) {
        const error = '阿里云OSS配置不完整'
        console.error('❌ 阿里云OSS配置不完整')
        throw new Error(error)
      }

      console.log('⚙️ 阿里云OSS配置:', {
        bucket: this.config.bucket,
        region: this.config.region,
        hasAccessKey: !!this.config.accessKey,
        hasSecretKey: !!this.config.secretKey,
        path: this.config.path,
        domain: this.config.domain
      })

      // 生成文件路径和名称
      const generatedFilename = this.generateFilename(filename)
      const objectKey = this.config.path ? `${this.config.path}${generatedFilename}` : generatedFilename
      
      console.log('📝 阿里云OSS上传参数:', {
        objectKey,
        generatedFilename,
        contentType: blob.type || 'image/jpeg'
      })

      // 模拟进度
      if (onProgress) {
        onProgress({ loaded: 0, total: 100, percentage: 0 })
      }

      // 构建OSS endpoint
      const endpoint = `https://${this.config.bucket}.${this.config.region}.aliyuncs.com`
      const uploadUrl = `${endpoint}/${objectKey}`
      
      console.log('🌐 阿里云OSS上传URL:', uploadUrl)

      // 使用简单的PUT请求上传（需要预签名URL或者其他认证方式）
      // 这里先实现一个基础版本，实际使用需要根据阿里云OSS的API要求调整
      console.log('⬆️ 开始阿里云OSS上传...')
      
      // 暂时返回错误，提示需要配置为S3兼容模式
      throw new Error('阿里云OSS上传需要使用S3兼容模式。请在设置中选择"Amazon S3"并配置endpoint为OSS的S3兼容端点。')

    } catch (error) {
      console.error('❌ 阿里云OSS上传失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        provider: 'alioss',
        originalUrl: ''
      }
    }
  }
}

/**
 * 七牛云上传器
 */
class QiniuUploader extends ImageUploader {
  async upload(
    blob: Blob, 
    filename: string, 
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    console.log('🔄 QiniuUploader.upload 开始')
    console.log('📁 七牛云文件信息:', {
      filename,
      size: blob.size,
      type: blob.type
    })
    
    try {
      if (!this.validateConfig()) {
        const error = '七牛云配置无效'
        console.error('❌ 七牛云配置验证失败')
        throw new Error(error)
      }

      if (!this.config.accessKey || !this.config.secretKey || !this.config.bucket) {
        const error = '七牛云配置不完整'
        console.error('❌ 七牛云配置不完整')
        throw new Error(error)
      }

      console.log('⚙️ 七牛云配置:', {
        bucket: this.config.bucket,
        hasAccessKey: !!this.config.accessKey,
        hasSecretKey: !!this.config.secretKey,
        path: this.config.path,
        domain: this.config.domain
      })

      // 模拟进度
      if (onProgress) {
        onProgress({ loaded: 0, total: 100, percentage: 0 })
      }

      console.log('⬆️ 开始七牛云上传...')
      
      // 暂时返回错误，提示需要配置为S3兼容模式
      throw new Error('七牛云上传需要使用S3兼容模式。请在设置中选择"Amazon S3"并配置endpoint为七牛云的S3兼容端点。')

    } catch (error) {
      console.error('❌ 七牛云上传失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        provider: 'qiniu',
        originalUrl: ''
      }
    }
  }

  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      if (!this.validateConfig()) {
        return { success: false, error: '七牛云配置无效' }
      }

      if (!this.config.accessKey || !this.config.secretKey || !this.config.bucket) {
        return { success: false, error: '七牛云配置不完整' }
      }

      // 同样建议通过后端API代理
      return { success: false, error: '七牛云连接测试需要后端支持' }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败'
      }
    }
  }
}

/**
 * 图床上传管理器
 */
export class ImageUploadManager {
  private uploaders: Map<string, ImageUploader> = new Map()

  constructor(config: ImageConfig) {
    safeLog.info('🔧 创建 ImageUploadManager')
    safeLog.debug('⚙️ 配置信息:', sanitizeConfig(config))
    this.updateConfig(config)
  }

  /**
   * 更新配置
   */
  updateConfig(config: ImageConfig) {
    safeLog.debug('🔄 更新配置，清空现有上传器')
    this.uploaders.clear()
    
    safeLog.debug('🎯 根据提供商创建上传器:', config.provider)
    switch (config.provider) {
      case 'alioss':
        safeLog.debug('📦 创建阿里云OSS上传器')
        this.uploaders.set('alioss', new AliOSSUploader(config))
        break
      case 'qiniu':
        safeLog.debug('📦 创建七牛云上传器')
        this.uploaders.set('qiniu', new QiniuUploader(config))
        break
      case 's3':
        safeLog.debug('📦 创建S3上传器')
        this.uploaders.set('s3', new S3Uploader(config))
        break
      default:
        safeLog.debug('📦 默认创建S3上传器')
        this.uploaders.set('s3', new S3Uploader(config))
        break
    }
    
    safeLog.debug('✅ 配置更新完成，可用上传器:', Array.from(this.uploaders.keys()))
  }

  /**
   * 上传图片
   */
  async uploadImage(
    blob: Blob,
    filename: string,
    provider?: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    console.log('🔄 ImageUploadManager.uploadImage 开始')
    console.log('📁 上传参数:', {
      blobSize: blob.size,
      blobType: blob.type,
      filename,
      provider: provider || 'default'
    })
    
    // 确定使用的上传器
    const uploaderKey = provider || this.getDefaultProvider()
    console.log('🔧 选择的上传器:', uploaderKey)
    
    const uploader = this.uploaders.get(uploaderKey)
    console.log('📋 可用的上传器:', Array.from(this.uploaders.keys()))

    if (!uploader) {
      const error = `不支持的图床服务: ${uploaderKey}`
      console.error('❌ 上传器不存在:', error)
      return {
        success: false,
        error,
        provider: uploaderKey,
        originalUrl: ''
      }
    }

    try {
      console.log('⬆️ 开始调用上传器上传...')
      const result = await uploader.upload(blob, filename, onProgress)
      console.log('📊 上传器返回结果:', result)
      return result
    } catch (error) {
      console.error('❌ 上传器抛出异常:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : '上传失败',
        provider: uploaderKey,
        originalUrl: ''
      }
    }
  }

  /**
   * 批量上传图片
   */
  async uploadImages(
    files: { blob: Blob; filename: string }[],
    provider?: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = []
    
    for (let i = 0; i < files.length; i++) {
      const { blob, filename } = files[i]
      
      const result = await this.uploadImage(blob, filename, provider)
      results.push(result)
      
      if (onProgress) {
        onProgress(i + 1, files.length)
      }
    }
    
    return results
  }

  /**
   * 获取默认提供商
   */
  private getDefaultProvider(): string {
    const keys = Array.from(this.uploaders.keys())
    // 优先使用 s3，其次使用第一个可用上传器
    if (keys.includes('s3')) return 's3'
    return keys[0] || 's3'
  }

  /**
   * 获取当前提供商
   */
  getCurrentProvider(): string {
    return this.getDefaultProvider()
  }

  /**
   * 获取支持的提供商列表
   */
  getSupportedProviders(): string[] {
    return Array.from(this.uploaders.keys())
  }

  /**
   * 检查提供商是否可用
   */
  isProviderAvailable(provider: string): boolean {
    const uploader = this.uploaders.get(provider)
    return uploader ? uploader['validateConfig']() : false
  }

  /**
   * 测试图床连接
   */
  async testConnection(provider?: string): Promise<{ success: boolean; error?: string }> {
    // 确定使用的上传器
    const uploaderKey = provider || this.getDefaultProvider()
    const uploader = this.uploaders.get(uploaderKey)

    if (!uploader) {
      return {
        success: false,
        error: `不支持的图床服务: ${uploaderKey}`
      }
    }

    try {
      return await uploader.testConnection()
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '连接测试失败'
      }
    }
  }
}

/**
 * 图片链接替换器
 */
export class ImageLinkReplacer {
  private uploadManager: ImageUploadManager
  private replacementMap: Map<string, string> = new Map()
  private failedUrls: Set<string> = new Set()
  private failureReasons: Map<string, string> = new Map()

  constructor(uploadManager: ImageUploadManager) {
    this.uploadManager = uploadManager
  }

  /**
   * 处理Markdown中的图片
   */
  async processMarkdownImages(
    markdown: string,
    onProgress?: (processed: number, total: number) => void,
    sourceUrl?: string,
    concurrency: number = 3
  ): Promise<string> {
    safeLog.info('🔄 开始处理Markdown图片')
    safeLog.debug('📝 输入参数:', {
      markdownLength: markdown.length,
      hasProgressCallback: !!onProgress,
      sourceUrl: sanitizeUrl(sourceUrl || ''),
      concurrency
    })
    
    // 提取所有图片链接，支持更多格式
    const imageRegexes = [
      /!\[([^\]]*)\]\(([^)]+)\)/g, // 标准 Markdown 图片
      /!\[([^\]]*)\]\(([^)]+)\s+"([^"]+)"\)/g, // 带 title 的 Markdown 图片
      /<img[^>]+src=["']([^"']+)["'][^>]*>/gi // HTML img 标签
    ]
    
    const allMatches: Array<{ fullMatch: string; alt: string; url: string; title?: string }> = []
    
    for (const regex of imageRegexes) {
      const matches = Array.from(markdown.matchAll(regex))
      for (const match of matches) {
        if (regex === imageRegexes[2]) { // HTML img 标签
          allMatches.push({
            fullMatch: match[0],
            alt: '',
            url: match[1]
          })
        } else if (regex === imageRegexes[1]) { // 带 title
          allMatches.push({
            fullMatch: match[0],
            alt: match[1],
            url: match[2],
            title: match[3]
          })
        } else { // 标准格式
          allMatches.push({
            fullMatch: match[0],
            alt: match[1],
            url: match[2]
          })
        }
      }
    }
    
    safeLog.info(`🔍 找到 ${allMatches.length} 个图片需要处理`)
    
    if (allMatches.length === 0) {
      safeLog.info('⚠️ 没有找到图片，直接返回原始内容')
      return markdown
    }

    safeLog.debug('📷 图片列表:')
    allMatches.forEach((match, index) => {
      safeLog.debug(`  ${index + 1}. ${sanitizeUrl(match.url)}`)
    })

    // 使用并发限制处理图片
    const limit = pLimit(concurrency)
    let processedCount = 0
    let resultMarkdown = markdown

    const processImageWithRetry = async (match: typeof allMatches[0]) => {
      const { fullMatch, alt, url, title } = match
      
      try {
        // 跳过已经处理过的图片
        if (this.replacementMap.has(url)) {
          const newUrl = this.replacementMap.get(url)!
          const replacement = title 
            ? `![${alt}](${newUrl} "${title}")`
            : fullMatch.includes('<img') 
              ? `<img src="${newUrl}" alt="${alt}" />`
              : `![${alt}](${newUrl})`
          resultMarkdown = resultMarkdown.replace(fullMatch, replacement)
          return
        }

        // 使用重试机制处理图片
        const processedUrl = await pRetry(
          () => this.processImageWithRetry(url, sourceUrl),
          {
            retries: 2,
            minTimeout: 1000,
            maxTimeout: 5000,
            onFailedAttempt: (error) => {
              safeLog.warn(`⚠️ 图片处理重试 ${error.attemptNumber}/${error.retriesLeft + error.attemptNumber}: ${sanitizeUrl(url)}`)
            }
          }
        )
        
        if (processedUrl && processedUrl !== url) {
          const replacement = title 
            ? `![${alt}](${processedUrl} "${title}")`
            : fullMatch.includes('<img') 
              ? `<img src="${processedUrl}" alt="${alt}" />`
              : `![${alt}](${processedUrl})`
          resultMarkdown = resultMarkdown.replace(fullMatch, replacement)
          this.replacementMap.set(url, processedUrl)
        }

      } catch (error) {
        safeLog.error(`❌ 处理图片失败: ${sanitizeUrl(url)}`, error instanceof Error ? error.message : '处理失败')
        this.failedUrls.add(url)
        this.failureReasons.set(url, error instanceof Error ? error.message : '处理失败')
      }

      processedCount++
      if (onProgress) {
        onProgress(processedCount, allMatches.length)
      }
    }

    // 并发处理所有图片
    await Promise.all(allMatches.map(match => limit(() => processImageWithRetry(match))))

    safeLog.info('✅ Markdown图片处理完成')
    safeLog.debug('📝 最终结果:', {
      originalLength: resultMarkdown.length,
      hasChanges: this.replacementMap.size > 0,
      processedCount: this.replacementMap.size,
      failedCount: this.failedUrls.size
    })

    return resultMarkdown
  }

  /**
   * 处理单个图片（带重试逻辑）
   */
  private async processImageWithRetry(imageUrl: string, sourceUrl?: string): Promise<string> {
    const result = await this.processImage(imageUrl, sourceUrl)
    
    // 如果返回原始URL，抛出错误以触发重试
    if (result === imageUrl) {
      throw new Error('图片处理失败，返回了原始URL')
    }
    
    return result
  }

  /**
   * 处理单个图片
   */
  private async processImage(imageUrl: string, sourceUrl?: string): Promise<string> {
    console.log('🖼️ processImage 开始处理图片:', imageUrl)
    console.log('🌐 源URL:', sourceUrl || 'none')
    
    try {
      console.log('📥 开始下载图片:', imageUrl)
      
      let blob: Blob
      let downloadMethod = ''
      
      // 尝试通过content script下载图片（避免CORS问题）
      try {
        console.log('🔄 尝试通过content script下载图片...')
        blob = await this.downloadImageViaContentScript(imageUrl)
        downloadMethod = 'content-script'
        console.log('✅ 通过content script下载成功')
      } catch (contentScriptError) {
        console.warn('⚠️ content script下载失败，尝试直接下载:', contentScriptError)
        
        // 如果content script下载失败，尝试直接下载
        console.log('🔄 尝试直接下载...')
        
        // 尝试多种请求方式
        let response: Response;
        
        try {
          // 方式1：带referer但不带credentials
          const headers: HeadersInit = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'image/*,*/*;q=0.8'
          }
          
          if (sourceUrl) {
            try {
              const sourceOrigin = new URL(sourceUrl).origin
              headers['Referer'] = sourceUrl
              headers['Origin'] = sourceOrigin
              console.log('🌐 添加referer头:', sourceUrl)
            } catch (e) {
              console.warn('⚠️ 无效的源URL，跳过referer设置:', sourceUrl)
            }
          }
          
          response = await fetch(imageUrl, {
            method: 'GET',
            mode: 'cors',
            credentials: 'omit', // 不包含cookie，避免CORS限制
            headers
          })
          downloadMethod = '方式1(带referer，无credentials)'
        } catch (error1) {
          console.warn('⚠️ 方式1失败，尝试方式2:', error1)
          
          try {
            // 方式2：最简单的请求
            response = await fetch(imageUrl, {
              method: 'GET',
              mode: 'cors',
              credentials: 'omit'
            })
            downloadMethod = '方式2(简单请求)'
          } catch (error2) {
            console.warn('⚠️ 方式2失败，尝试方式3:', error2)
            
            // 方式3：no-cors模式（可能获取不到响应内容，但至少不会被CORS阻止）
            response = await fetch(imageUrl, {
              method: 'GET',
              mode: 'no-cors'
            })
            downloadMethod = '方式3(no-cors模式)'
          }
        }

        console.log('📡 直接下载响应状态:', {
          method: downloadMethod,
          status: response.status,
          statusText: response.statusText,
          ok: response.ok,
          headers: {
            contentType: response.headers.get('content-type'),
            contentLength: response.headers.get('content-length')
          }
        })

        if (!response.ok) {
          const errorMsg = `HTTP ${response.status}: ${response.statusText}`
          console.error('❌ 下载失败:', errorMsg)
          const error = new Error(errorMsg)
          ;(error as any).httpStatus = response.status
          throw error
        }

        blob = await response.blob()
        console.log('📦 直接下载完成:', {
          method: downloadMethod,
          size: blob.size,
          type: blob.type
        })
      }
      
      console.log('📦 图片下载完成:', {
        size: blob.size,
        type: blob.type,
        url: imageUrl,
        method: downloadMethod
      })
      
      // 验证下载的文件
      if (blob.size === 0) {
        const errorMsg = '下载的文件大小为0'
        console.error('❌ 下载验证失败:', errorMsg)
        this.failedUrls.add(imageUrl)
        throw new Error(errorMsg)
      }
      
      if (!blob.type.startsWith('image/') && blob.type !== '') {
        const errorMsg = `下载的文件类型不是图片: ${blob.type}`
        console.warn('⚠️ 文件类型警告:', errorMsg)
        // 不抛出错误，继续尝试上传
      }
      
      // 生成文件名
      const filename = this.extractFilename(imageUrl)
      console.log('📝 生成文件名:', filename)
      
      console.log('⬆️ 开始上传到图床...')
      // 上传图片
      const result = await this.uploadManager.uploadImage(blob, filename)
      
      console.log('📊 上传结果:', {
        success: result.success,
        url: result.url,
        error: result.error,
        provider: result.provider,
        originalUrl: imageUrl
      })
      
      if (result.success && result.url) {
        console.log('✅ 图片处理成功:', imageUrl, '→', result.url)
        return result.url
      } else {
        const errorMsg = `上传失败: ${result.error || '未知错误'}`
        console.error('❌ 图片上传失败:', errorMsg)
        this.failedUrls.add(imageUrl)
        throw new Error(errorMsg)
      }

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '处理失败'
      console.error('❌ 处理图片失败:', imageUrl, errorMsg)
      console.error('❌ 错误详情:', {
        message: errorMsg,
        name: error instanceof Error ? error.name : 'Unknown',
        stack: error instanceof Error ? error.stack : undefined
      })
      this.failedUrls.add(imageUrl)
      
      // 将错误信息添加到失败URL的映射中，方便后续获取
      this.failureReasons.set(imageUrl, errorMsg)
      
      return imageUrl // 返回原始链接
    }
  }

  /**
   * 通过content script下载图片
   */
  private async downloadImageViaContentScript(imageUrl: string): Promise<Blob> {
    return new Promise((resolve, reject) => {
      // 获取当前活动标签页
      browser.tabs.query({ active: true, currentWindow: true }).then(([activeTab]) => {
        if (!activeTab || !activeTab.id) {
          reject(new Error('无法获取当前标签页'))
          return
        }

        // 向content script发送下载请求
        browser.tabs.sendMessage(activeTab.id, {
          type: 'DOWNLOAD_IMAGE',
          imageUrl: imageUrl
        }).then((response) => {
          if (response && response.success && response.blob) {
            // 将base64转换为blob
            const byteCharacters = atob(response.blob)
            const byteNumbers = new Array(byteCharacters.length)
            for (let i = 0; i < byteCharacters.length; i++) {
              byteNumbers[i] = byteCharacters.charCodeAt(i)
            }
            const byteArray = new Uint8Array(byteNumbers)
            const blob = new Blob([byteArray], { type: response.contentType || 'image/jpeg' })
            resolve(blob)
          } else {
            reject(new Error(response?.error || '下载失败'))
          }
        }).catch(reject)
      }).catch(reject)
    })
  }

  /**
   * 从URL中提取文件名
   */
  private extractFilename(url: string): string {
    try {
      const urlObj = new URL(url)
      const pathname = urlObj.pathname
      const filename = pathname.split('/').pop() || 'image'
      
      // 如果没有扩展名，添加默认扩展名
      if (!filename.includes('.')) {
        return `${filename}.jpg`
      }
      
      return filename
    } catch {
      return `image_${Date.now()}.jpg`
    }
  }

  /**
   * 处理单个图片
   */
  async processSingleImage(imageUrl: string, sourceUrl?: string): Promise<UploadResult> {
    console.log('🖼️ processSingleImage 开始处理单个图片:', imageUrl)
    console.log('🌐 源URL:', sourceUrl || 'none')
    
    try {
      const uploadedUrl = await this.processImage(imageUrl, sourceUrl)
      
      if (uploadedUrl && uploadedUrl !== imageUrl) {
        console.log('✅ 单个图片处理成功:', uploadedUrl)
        return {
          success: true,
          url: uploadedUrl,
          provider: this.uploadManager.getCurrentProvider(),
          originalUrl: imageUrl
        }
      } else {
        const error = '图片上传失败，返回了原始URL'
        console.warn('⚠️ 单个图片处理失败:', error)
        return {
          success: false,
          error: error,
          provider: this.uploadManager.getCurrentProvider(),
          originalUrl: imageUrl
        }
      }
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : '处理失败'
      console.error('❌ 单个图片处理异常:', errorMsg)
      return {
        success: false,
        error: errorMsg,
        provider: this.uploadManager.getCurrentProvider(),
        originalUrl: imageUrl
      }
    }
  }

  /**
   * 清除替换缓存
   */
  clearCache() {
    this.replacementMap.clear()
    this.failedUrls.clear()
    this.failureReasons.clear()
  }

  /**
   * 获取替换统计
   */
  getStats() {
    return {
      processedCount: this.replacementMap.size,
      failedCount: this.failedUrls.size,
      replacements: Array.from(this.replacementMap.entries()),
      failedUrls: Array.from(this.failedUrls),
      failureReasons: Array.from(this.failureReasons.entries())
    }
  }
}