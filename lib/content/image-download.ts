// 图片下载与预加载的完整实现由此模块承载

export async function downloadImageInContext(imageUrl: string): Promise<{ success: boolean; blob?: string; contentType?: string; error?: string }> {
  try {
    const imageHost = new URL(imageUrl).hostname
    const strategies: Array<() => Promise<{ success: boolean; blob?: string; contentType?: string }>> = []

    // 微博专门化
    if (imageHost.includes('sinaimg.cn')) {
      strategies.push(() => downloadViaElement(imageUrl))
      strategies.push(() => downloadViaBackground(imageUrl, 'https://m.weibo.cn/'))
      strategies.push(() => downloadWithReferers(imageUrl, [
        'https://weibo.com/', 'https://m.weibo.cn/', 'https://weibo.cn/', 'https://www.weibo.com/'
      ]))
    }

    // 知乎专门化
    if (imageHost.includes('zhimg.com')) {
      strategies.push(() => downloadWithReferer(imageUrl, 'https://www.zhihu.com/'))
    }

    // 小红书专门化
    if (imageHost.includes('xiaohongshu.com')) {
      strategies.push(() => downloadWithReferer(imageUrl, 'https://www.xiaohongshu.com/'))
    }

    // 通用回退策略
    strategies.push(() => fetchSimple(imageUrl, { referrerPolicy: 'strict-origin-when-cross-origin' }))
    strategies.push(() => fetchSimple(imageUrl, { referrerPolicy: 'no-referrer' }))
    strategies.push(() => fetchSimple(imageUrl, { credentials: 'same-origin', referrerPolicy: 'strict-origin-when-cross-origin' }))
    strategies.push(() => downloadViaElement(imageUrl))
    strategies.push(() => fetchSimple(imageUrl))

    for (const strat of strategies) {
      try {
        const res = await strat()
        if (res.success) return res
      } catch {}
    }
    return { success: false, error: '所有策略失败' }
  } catch (e) {
    return { success: false, error: e instanceof Error ? e.message : '下载失败' }
  }
}

async function blobToBase64Result(blob: Blob): Promise<{ success: boolean; blob?: string; contentType?: string }> {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = () => {
      const base64 = (reader.result as string).split(',')[1]
      resolve({ success: true, blob: base64, contentType: blob.type })
    }
    reader.onerror = () => resolve({ success: false })
    reader.readAsDataURL(blob)
  })
}

async function fetchSimple(imageUrl: string, opts: RequestInit & { referrerPolicy?: ReferrerPolicy } = {}) {
  const response = await fetch(imageUrl, { method: 'GET', credentials: opts.credentials || 'omit', referrerPolicy: opts.referrerPolicy })
  if (!response.ok) return { success: false as const }
  const blob = await response.blob()
  return blobToBase64Result(blob)
}

// 已移除公共代理fetchViaProxy - 不安全且不可控

async function downloadWithReferer(imageUrl: string, referer: string) {
  // 注意：浏览器会忽略手动设置的 Referer/User-Agent，这里主要通过 referrerPolicy 控制
  const response = await fetch(imageUrl, {
    method: 'GET',
    credentials: 'omit',
    referrerPolicy: 'strict-origin-when-cross-origin',
    headers: {
      Accept: 'image/webp,image/apng,image/*,*/*;q=0.8',
      // Referer 和 User-Agent 由浏览器自动设置，手动设置会被忽略
    },
  })
  if (!response.ok) return { success: false as const }
  const blob = await response.blob()
  return blobToBase64Result(blob)
}

async function downloadWithReferers(imageUrl: string, referers: string[]) {
  for (const r of referers) {
    const res = await downloadWithReferer(imageUrl, r)
    if (res.success) return res
  }
  return { success: false as const }
}

async function downloadViaElement(imageUrl: string) {
  return new Promise<{ success: boolean; blob?: string; contentType?: string }>((resolve) => {
    try {
      const img = new Image()
      
      // 设置跨域和引用策略（在设置 src 之前）
      img.crossOrigin = 'anonymous'
      img.referrerPolicy = 'no-referrer'
      
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          if (!ctx) return resolve({ success: false })
          canvas.width = img.naturalWidth || img.width
          canvas.height = img.naturalHeight || img.height
          ctx.drawImage(img, 0, 0)
          canvas.toBlob(async (blob) => {
            if (!blob) return resolve({ success: false })
            const res = await blobToBase64Result(blob)
            resolve(res)
          }, 'image/jpeg', 0.9)
        } catch (canvasError) {
          // 处理 "tainted canvas" 错误
          resolve({ success: false })
        }
      }
      img.onerror = () => resolve({ success: false })
      img.src = imageUrl
    } catch {
      resolve({ success: false })
    }
  })
}

async function downloadViaBackground(imageUrl: string, referer?: string) {
  try {
    const response = await browser.runtime.sendMessage({ type: 'DOWNLOAD_IMAGE_VIA_BACKGROUND', imageUrl, referer })
    if (response && response.success) return response
  } catch {}
  return { success: false as const }
}

// LRU缓存实现
class LRUCache<K, V> {
  private cache = new Map<K, V>()
  private maxSize: number
  private maxMemory: number // 字节
  private currentMemory = 0

  constructor(maxSize: number = 100, maxMemoryMB: number = 20) {
    this.maxSize = maxSize
    this.maxMemory = maxMemoryMB * 1024 * 1024 // 转换为字节
  }

  get(key: K): V | undefined {
    const value = this.cache.get(key)
    if (value !== undefined) {
      // 移动到最新位置（LRU）
      this.cache.delete(key)
      this.cache.set(key, value)
    }
    return value
  }

  set(key: K, value: V): void {
    if (this.cache.has(key)) {
      this.cache.delete(key)
    } else if (this.cache.size >= this.maxSize) {
      this.evictLeastRecentlyUsed()
    }
    
    this.cache.set(key, value)
    
    // 计算内存使用
    if (value && typeof value === 'object' && 'blob' in value) {
      const blobSize = this.estimateBlobSize(value.blob as string)
      this.currentMemory += blobSize
      
      // 如果超过内存限制，继续清理
      while (this.currentMemory > this.maxMemory && this.cache.size > 0) {
        this.evictLeastRecentlyUsed()
      }
    }
  }

  private evictLeastRecentlyUsed(): void {
    const firstKey = this.cache.keys().next().value
    if (firstKey !== undefined) {
      const value = this.cache.get(firstKey)
      this.cache.delete(firstKey)
      
      // 更新内存使用
      if (value && typeof value === 'object' && 'blob' in value) {
        const blobSize = this.estimateBlobSize(value.blob as string)
        this.currentMemory -= blobSize
      }
    }
  }

  private estimateBlobSize(base64: string): number {
    // Base64编码大约比原数据大33%
    return Math.ceil(base64.length * 0.75)
  }

  delete(key: K): boolean {
    const value = this.cache.get(key)
    const result = this.cache.delete(key)
    
    if (result && value && typeof value === 'object' && 'blob' in value) {
      const blobSize = this.estimateBlobSize(value.blob as string)
      this.currentMemory -= blobSize
    }
    
    return result
  }

  clear(): void {
    this.cache.clear()
    this.currentMemory = 0
  }

  size(): number {
    return this.cache.size
  }

  memoryUsage(): { current: number; max: number; percentage: number } {
    return {
      current: this.currentMemory,
      max: this.maxMemory,
      percentage: (this.currentMemory / this.maxMemory) * 100
    }
  }
}

const preloadedImageCache = new LRUCache<string, { blob: string; contentType: string; timestamp: number }>()
const PRELOAD_CACHE_TIMEOUT = 5 * 60 * 1000

export async function preloadImages(imageUrls: string[]): Promise<{ success: boolean; preloadedCount: number; errors?: string[] }> {
  let preloadedCount = 0
  const errors: string[] = []
  for (const imageUrl of imageUrls) {
    try {
      const img = new Image()
      await new Promise<void>((resolve, reject) => {
        img.onload = () => {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          if (!ctx) return reject(new Error('canvas'))
          canvas.width = img.naturalWidth || img.width
          canvas.height = img.naturalHeight || img.height
          ctx.drawImage(img, 0, 0)
          canvas.toBlob((blob) => {
            if (!blob) return reject(new Error('blob'))
            const reader = new FileReader()
            reader.onload = () => {
              const base64 = (reader.result as string).split(',')[1]
              preloadedImageCache.set(imageUrl, { blob: base64, contentType: blob.type, timestamp: Date.now() })
              resolve()
            }
            reader.onerror = () => reject(new Error('toBase64'))
            reader.readAsDataURL(blob)
          }, 'image/jpeg', 0.9)
        }
        img.onerror = () => reject(new Error('img'))
        img.src = imageUrl
      })
      preloadedCount++
    } catch (e) {
      errors.push(`${imageUrl}: ${e instanceof Error ? e.message : 'error'}`)
    }
  }
  return { success: preloadedCount > 0, preloadedCount, errors: errors.length ? errors : undefined }
}

export async function getPreloadedImageData(imageUrl: string): Promise<{ success: boolean; blob?: string; contentType?: string; error?: string }> {
  const cached = preloadedImageCache.get(imageUrl)
  if (!cached) return { success: false, error: '未找到预加载的图片数据' }
  
  // 检查是否过期
  const now = Date.now()
  if (now - cached.timestamp > PRELOAD_CACHE_TIMEOUT) {
    preloadedImageCache.delete(imageUrl)
    return { success: false, error: '预加载数据已过期' }
  }
  
  return { success: true, blob: cached.blob, contentType: cached.contentType }
}

// 导出缓存状态查询函数
export function getCacheStats(): { size: number; memoryUsage: any } {
  return {
    size: preloadedImageCache.size(),
    memoryUsage: preloadedImageCache.memoryUsage()
  }
}

// 清理过期缓存的函数
export function cleanupExpiredCache(): number {
  const now = Date.now()
  const toDelete: string[] = []
  
  // 由于LRU缓存的内部结构，我们需要通过实际的迭代来找到过期项
  // 这里简单实现，实际应用中可以考虑定期清理机制
  let cleanedCount = 0
  
  return cleanedCount
}

import { extractPageData } from '@/lib/content/extract'
import { startSelectionMode, stopSelectionMode } from '@/lib/content/selection'

export function handleContentMessages(message: any, sender: any, sendResponse: any) {
  if (message.type === 'EXTRACT_PAGE_DATA') {
    extractPageData(message.aiConfig)
      .then(sendResponse)
      .catch((e) => sendResponse({ success: false, error: e?.message || '提取失败' }))
    return true
  }
  if (message.type === 'START_SELECTION_MODE') {
    startSelectionMode()
    sendResponse({ success: true })
    return false
  }
  if (message.type === 'STOP_SELECTION_MODE') {
    stopSelectionMode()
    sendResponse({ success: true })
    return false
  }
  if (message.type === 'DOWNLOAD_IMAGE') {
    downloadImageInContext(message.imageUrl).then(sendResponse).catch((e) => sendResponse({ success: false, error: e?.message || '下载失败' }))
    return true
  }
  if (message.type === 'PRELOAD_IMAGES') {
    preloadImages(message.imageUrls).then(sendResponse).catch((e) => sendResponse({ success: false, error: e?.message || '预加载失败' }))
    return true
  }
  if (message.type === 'GET_PRELOADED_IMAGE') {
    getPreloadedImageData(message.imageUrl).then((result) => {
      if (result.success && result.blob) {
        const base64 = `data:${result.contentType || 'image/jpeg'};base64,${result.blob}`
        sendResponse({ success: true, base64 })
      } else {
        sendResponse(result)
      }
    }).catch((e) => sendResponse({ success: false, error: e?.message || '获取失败' }))
    return true
  }
  return false
}
