import TurndownService from 'turndown'

export interface TwitterExtractResult {
  html: string
  markdown: string
  title: string
}

export function extractTwitter(turndown: TurndownService): TwitterExtractResult | null {
  try {
    const article = document.querySelector('article')
    if (!article) return null

    const cleanedHTML = cleanTwitterArticle(article as HTMLElement)
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = cleanedHTML
    const textContent = tempDiv.textContent?.trim() || ''
    const title = textContent ? generateTwitterTitle(textContent) : document.title || '来自 x.com 的帖子'

    const finalHTML = `<div class="twitter-post">\n${cleanedHTML}\n</div>`
    const markdown = turndown.turndown(finalHTML)

    if (!markdown.trim()) return { html: finalHTML, markdown: turndown.turndown(cleanedHTML), title }
    return { html: finalHTML, markdown, title }
  } catch (e) {
    console.error('推特内容提取失败:', e)
    const url = window.location.href
    const html = `<div class="twitter-post">\n  <p>此推特帖子可能包含媒体内容，详情见:</p>\n  <p><a href="${url}">${url}</a></p>\n</div>`
    return { html, markdown: turndown.turndown(html), title: document.title || '来自 x.com 的帖子' }
  }
}

function cleanTwitterArticle(article: HTMLElement): string {
  const cloned = article.cloneNode(true) as HTMLElement

  // 移除明显无关的关注/头像链接
  cloned.querySelectorAll('a[href^="/"]').forEach((link) => {
    const text = link.textContent?.trim() || ''
    const hasImage = link.querySelector('img')
    const href = link.getAttribute('href') || ''
    const isUserLink = /^\/[A-Za-z0-9_]+$/.test(href)
    if (hasImage && isUserLink) {
      link.remove()
      return
    }
    if (text === 'Click to Follow' || /^Follow\s+\w+$/i.test(text) || text === '关注') {
      link.remove()
      return
    }
  })

  // 解除图片外层链接包装，保留图片
  cloned.querySelectorAll('a').forEach((link) => {
    const images = link.querySelectorAll('img')
    if (images.length > 0) {
      images.forEach((img) => link.parentNode?.insertBefore(img.cloneNode(true), link))
      link.remove()
    }
  })

  // 返回清理后的 HTML
  return cloned.innerHTML || cloned.outerHTML || ''
}

function generateTwitterTitle(content: string): string {
  const clean = content.replace(/\s+/g, ' ').trim()
  if (clean.length <= 50) return clean
  const firstLine = clean.split('\n')[0]?.trim()
  if (firstLine && firstLine.length <= 50) return firstLine
  const firstSentence = clean.match(/^[^。！？.!?]*[。！？.!?]/)?.[0]
  if (firstSentence && firstSentence.length <= 50) return firstSentence
  return clean.substring(0, 47) + '...'
}
