import TurndownService from 'turndown'

export interface XHSExtractResult {
  html: string
  markdown: string
  coverImage?: string
  title?: string
}

export function extractXiaohongshu(turndown: TurndownService): XHSExtractResult | null {
  try {
    // 标题
    const titleElement = document.querySelector('#detail-title')
    const title = titleElement?.textContent?.trim() || ''

    // 正文
    const descElement = document.querySelector('#detail-desc')
    const desc = processXiaohongshuContent(descElement)

    // 图片（笔记中的图片轮播）
    const swiperWrapper = document.querySelector('.swiper-wrapper')
    const images = (swiperWrapper?.querySelectorAll('img') || []) as NodeListOf<HTMLImageElement>

    // 评论区
    const commentsContainer = document.querySelector('.comments-container')
    const processedComments = extractXiaohongshuComments(commentsContainer)

    // 封面
    const coverImage = extractXiaohongshuCoverImage()

    let html = ''
    if (title) html += `<h1>${title}</h1>\n\n`
    if (desc) html += `<div class="xiaohongshu-content">${desc}</div>\n\n`

    if (images && images.length > 0) {
      html += '<div class="xiaohongshu-images">\n'
      images.forEach((img, index) => {
        const src = img.getAttribute('src') || img.getAttribute('data-src')
        const alt = img.getAttribute('alt') || `小红书图片${index + 1}`
        if (src) html += `  <img src="${src}" alt="${alt}" />\n`
      })
      html += '</div>\n\n'
    }

    if (processedComments) {
      html += '<div class="xiaohongshu-comments">\n'
      html += `<h2>评论</h2>\n`
      html += `${processedComments}\n`
      html += '</div>\n'
    }

    if (!html.trim()) return null

    return {
      html: html.trim(),
      markdown: turndown.turndown(html),
      coverImage: coverImage || undefined,
      title: title || undefined,
    }
  } catch (e) {
    console.error('小红书内容提取失败:', e)
    return null
  }
}

function processXiaohongshuContent(descElement: Element | null): string | null {
  if (!descElement) return null
  try {
    let content = (descElement as HTMLElement).innerHTML?.trim() || ''
    if (content.includes('<br>') || content.includes('<p>') || content.includes('<div>')) {
      return content
    }
    const textContent = descElement.textContent?.trim() || ''
    if (!textContent) return null

    const computedStyle = window.getComputedStyle(descElement)
    const whiteSpace = computedStyle.whiteSpace

    if (whiteSpace === 'pre-wrap' || whiteSpace === 'pre-line' || whiteSpace === 'pre' || textContent.includes('\n')) {
      let processed = textContent
        .replace(/\r\n/g, '\n')
        .replace(/\r/g, '\n')
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#39;')
        .replace(/\n\s*\n\s*\n+/g, '\n\n')
        .replace(/\n/g, '<br>\n')
        .replace(/(<br>\s*\n){2,}/g, '</p>\n\n<p>')
        .replace(/^<\/p>\s*\n*/, '')
        .replace(/\s*\n*<p>$/, '')

      if (processed.includes('</p>')) processed = `<p>${processed}</p>`
      return processed
    }

    if (content.includes('<')) return content
    return textContent
  } catch (e) {
    console.error('小红书正文处理失败:', e)
    return (descElement as HTMLElement).innerHTML?.trim() || descElement.textContent?.trim() || null
  }
}

function extractXiaohongshuCoverImage(): string | null {
  try {
    const ogImageMeta = document.querySelector('meta[property="og:image"]') || document.querySelector('meta[name="og:image"]')
    const ogImageUrl = ogImageMeta?.getAttribute('content')
    if (ogImageUrl && isValidImageUrl(ogImageUrl)) return resolveImageUrl(ogImageUrl)
    return null
  } catch (e) {
    console.error('小红书封面图片提取失败:', e)
    return null
  }
}

function extractXiaohongshuComments(commentsContainer: Element | null): string | null {
  if (!commentsContainer) return null
  try {
    let commentsHtml = ''
    const processed = new Set<string>()

    const commentElements = commentsContainer.querySelectorAll('.comment-item, .comment, [class*="comment"]')
    if (commentElements.length === 0) {
      const authors = commentsContainer.querySelectorAll('.author')
      const noteTexts = commentsContainer.querySelectorAll('.note-text')
      if (authors.length === noteTexts.length && authors.length > 0) {
        for (let i = 0; i < authors.length; i++) {
          const author = removeEmojis(authors[i].textContent?.trim() || '')
          const noteText = removeEmojis(noteTexts[i].textContent?.trim() || '')
          if (author && noteText) {
            const id = `${author}:${noteText}`
            if (!processed.has(id)) {
              processed.add(id)
              commentsHtml += `<div class="comment-item">\n  <strong>${author}</strong>: ${noteText}\n</div>\n`
            }
          }
        }
      }
    } else {
      commentElements.forEach((el) => {
        const author = removeEmojis((el.querySelector('.author')?.textContent || '').trim())
        const text = removeEmojis((el.querySelector('.note-text')?.textContent || el.textContent || '').trim())
        if (author && text) {
          const id = `${author}:${text}`
          if (!processed.has(id)) {
            processed.add(id)
            commentsHtml += `<div class="comment-item">\n  <strong>${author}</strong>: ${text}\n</div>\n`
          }
        }
      })
    }

    return commentsHtml || null
  } catch (e) {
    console.error('小红书评论提取失败:', e)
    return null
  }
}

function removeEmojis(text: string): string {
  if (!text) return ''
  return text.replace(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]|[\u{1F900}-\u{1F9FF}]|[\u{1F018}-\u{1F270}]|[\u{238C}]|[\u{FE00}-\u{FE0F}]|[\u{FE30}-\u{FE4F}]/gu, '')
}

function isValidImageUrl(url: string): boolean {
  try { new URL(url); return true } catch { return false }
}

function resolveImageUrl(url: string): string {
  if (url.startsWith('http')) return url
  if (url.startsWith('//')) return `${location.protocol}${url}`
  if (url.startsWith('/')) return `${location.origin}${url}`
  const base = location.href.substring(0, location.href.lastIndexOf('/') + 1)
  try { return new URL(url, base).href } catch { return url }
}
