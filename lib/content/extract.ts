import { AIContentExtractor, collectDOMElementsInfo, generatePageStructure } from '@/lib/ai-content-extractor'
import { SiteRule, findMatchingRule as findRule, extractContentByRule as extractByRule } from '@/lib/content/site-rules'
import { extractXiaohongshu } from '@/lib/content/sites/xiaohongshu'
import { extractTwitter } from '@/lib/content/sites/twitter'
import { turndownService } from '@/lib/content/turndown'

// use shared turndownService

export async function extractPageData(aiConfig?: any) {
  const contentData = await extractMainContentAsHTML(aiConfig)
  const coverImage = contentData.coverImage || extractCoverImage()
  const author = extractAuthorInfo()
  const publishDate = extractPublishDate()
  const pageTitle = contentData.title || document.title || '无标题'

  return {
    title: pageTitle,
    site: getSiteInfo(),
    url: window.location.href,
    timestamp: new Date().toLocaleString('zh-CN'),
    content: contentData.markdown,
    contentHTML: contentData.html,
    favicon: getFavicon(),
    coverImage,
    author,
    publishDate,
    aiAnalysis: contentData.aiAnalysis
  }
}

async function extractMainContentAsHTML(aiConfig?: any): Promise<{ html: string; markdown: string; aiAnalysis?: any; coverImage?: string; title?: string }> {
  const rule = findRule()
  if (rule) {
    if ((rule as SiteRule).isXiaohongshu && window.location.href.includes('xiaohongshu.com/explore/')) {
      const xr = extractXiaohongshu(turndownService)
      if (xr) return xr
    }
    if ((rule as SiteRule).isTwitter && window.location.href.includes('/status/')) {
      const tr = extractTwitter(turndownService)
      if (tr) return tr
    }
    const r = extractByRule(rule)
    if (r) return r
  }

  // AI 辅助路径
  let aiAnalysis: any = undefined
  try {
    if (aiConfig?.enabled) {
      const extractor = new AIContentExtractor(aiConfig)
      aiAnalysis = await extractor.analyzePageStructure({
        url: window.location.href,
        title: document.title,
        domElements: collectDOMElementsInfo(),
        pageStructure: generatePageStructure(),
      })
      if (aiAnalysis?.success && Array.isArray(aiAnalysis.selectors) && aiAnalysis.selectors.length > 0) {
        for (const selector of aiAnalysis.selectors) {
          const el = document.querySelector(selector)
          if (el && el.textContent && el.textContent.trim().length > 100) {
            const cleanedHTML = cleanHTML(el)
            return { html: cleanedHTML, markdown: turndownService.turndown(cleanedHTML), aiAnalysis }
          }
        }
      }
    }
  } catch {}

  // 传统启发式提取（简化版）
  const articleElement = document.querySelector('article') || document.querySelector('main') || document.body
  const cleanedHTML = cleanHTML(articleElement)
  return { html: cleanedHTML, markdown: turndownService.turndown(cleanedHTML), aiAnalysis }
}

function cleanHTML(element: Element | null): string {
  if (!element) return ''
  const cloned = element.cloneNode(true) as Element
  // 基础移除
  cloned.querySelectorAll('script,style,nav,aside,footer,header').forEach(n => n.remove())
  return (cloned as HTMLElement).innerHTML || (cloned as HTMLElement).outerHTML || ''
}

function getSiteInfo(): string {
  const hostname = window.location.hostname
  const siteName = document.querySelector('meta[property="og:site_name"]')?.getAttribute('content')
  return siteName || hostname
}

function getFavicon(): string {
  const favicon = document.querySelector('link[rel*="icon"]') as HTMLLinkElement
  if (favicon && favicon.href) return favicon.href
  return `${window.location.origin}/favicon.ico`
}

function extractCoverImage(): string | null {
  const ogImage = document.querySelector('meta[property="og:image"]')?.getAttribute('content')
  if (ogImage) return resolveImageUrl(ogImage)
  const twitterImage = document.querySelector('meta[name="twitter:image"]')?.getAttribute('content')
  if (twitterImage) return resolveImageUrl(twitterImage)
  return null
}

function resolveImageUrl(url: string): string {
  if (!url) return url
  try {
    if (url.startsWith('http')) return url
    if (url.startsWith('//')) return `${location.protocol}${url}`
    if (url.startsWith('/')) return `${location.origin}${url}`
    const base = location.href.substring(0, location.href.lastIndexOf('/') + 1)
    return new URL(url, base).href
  } catch { return url }
}

function extractAuthorInfo(): string | null {
  const metaCandidates = [
    'meta[name="author"]',
    'meta[property="article:author"]',
    'meta[name="byl"]',
  ]
  for (const sel of metaCandidates) {
    const val = document.querySelector(sel)?.getAttribute('content')?.trim()
    if (val) return val
  }
  return null
}

function extractPublishDate(): string | null {
  const metaCandidates = [
    'meta[property="article:published_time"]',
    'meta[name="pubdate"]',
    'time[datetime]'
  ]
  for (const sel of metaCandidates) {
    const el = document.querySelector(sel)
    const val = el?.getAttribute('content') || el?.getAttribute('datetime') || el?.textContent
    if (val) return val.trim()
  }
  return null
}

// site-specific logic moved to lib/content/sites/*
