import { turndownService } from '@/lib/content/turndown'

export interface SiteRule {
  domains: string[]
  selectors: string[]
  description: string
  isXiaohongshu?: boolean
  isTwitter?: boolean
}

// 使用共享的 turndown 实例

export const siteRules: SiteRule[] = [
  { domains: ['sspai.com'], selectors: ['.article-body'], description: '少数派文章正文' },
  { domains: ['xiaohongshu.com'], selectors: [], description: '小红书笔记', isXiaohongshu: true },
  { domains: ['x.com', 'twitter.com'], selectors: [], description: '推特帖子', isTwitter: true },
]

export function findMatchingRule(): SiteRule | null {
  const hostname = window.location.hostname
  for (const rule of siteRules) {
    for (const domain of rule.domains) {
      if (hostname.includes(domain)) {
        return rule
      }
    }
  }
  return null
}

export function extractContentByRule(rule: SiteRule): { html: string; markdown: string; coverImage?: string; title?: string } | null {
  // 小红书/推特特殊规则由上层专用逻辑处理；此处仅处理 selectors 方案
  for (const selector of rule.selectors) {
    const element = document.querySelector(selector)
    if (element && element.textContent && element.textContent.trim().length > 100) {
      const html = (element as HTMLElement).outerHTML
      return { html, markdown: turndownService.turndown(html) }
    }
  }
  return null
}
