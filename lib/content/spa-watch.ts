export function initSpaWatch(onChange: () => void) {
  let currentUrl = window.location.href

  function handleUrlChangeInternal() {
    const newUrl = window.location.href
    if (newUrl !== currentUrl) {
      currentUrl = newUrl
      onChange()
    }
  }

  function detectUrlChange() {
    const newUrl = window.location.href
    if (newUrl !== currentUrl) {
      const isTwitter = window.location.hostname.includes('x.com') || window.location.hostname.includes('twitter.com')
      if (isTwitter && newUrl.includes('/status/')) {
        const expectedTweetId = newUrl.match(/\/status\/(\d+)/)?.[1]
        if (expectedTweetId) {
          waitForContentLoad(expectedTweetId).then(() => handleUrlChangeInternal())
          return
        }
      }
      const delay = isTwitter ? 1500 : 500
      setTimeout(handleUrlChangeInternal, delay)
    }
  }

  function waitForContentLoad(expectedTweetId: string, maxWait: number = 3000): Promise<boolean> {
    return new Promise((resolve) => {
      const start = Date.now()
      const check = () => {
        if (Date.now() - start > maxWait) return resolve(false)
        const article = document.querySelector('article')
        if (!article || !article.textContent?.trim()) {
          setTimeout(check, 200)
          return
        }
        const urlTweetId = window.location.href.match(/\/status\/(\d+)/)?.[1]
        if (urlTweetId && urlTweetId === expectedTweetId) return resolve(true)
        setTimeout(check, 200)
      }
      check()
    })
  }

  window.addEventListener('popstate', () => setTimeout(detectUrlChange, 100))
  window.addEventListener('hashchange', () => setTimeout(detectUrlChange, 100))

  const observer = new MutationObserver(() => detectUrlChange())
  if (document.body) observer.observe(document.body, { childList: true, subtree: true })

  const originalPushState = history.pushState
  const originalReplaceState = history.replaceState
  history.pushState = function (...args: any[]) {
    originalPushState.apply(history, args as any)
    setTimeout(detectUrlChange, 100)
  } as any
  history.replaceState = function (...args: any[]) {
    originalReplaceState.apply(history, args as any)
    setTimeout(detectUrlChange, 100)
  } as any

  console.log('🎯 SPA监听机制已启动，当前URL:', currentUrl)
}
