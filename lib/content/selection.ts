let isSelectionMode = false
let selectionOverlay: HTMLDivElement | null = null
let selectionPanel: HTMLDivElement | null = null
let highlightedElement: Element | null = null
const selectedElements: Element[] = []

export function initSelection() {
  if (!selectionOverlay) {
    selectionOverlay = document.createElement('div')
    selectionOverlay.id = 'web-clipper-selection-overlay'
    selectionOverlay.style.cssText = `
      position: fixed; inset: 0; z-index: 999999; display: none;
      background: rgba(0,0,0,0.2); pointer-events: none;
    `
    document.body.appendChild(selectionOverlay)
  }
  if (!selectionPanel) {
    selectionPanel = document.createElement('div')
    selectionPanel.id = 'web-clipper-selection-panel'
    selectionPanel.style.cssText = `
      position: fixed; top: 20px; right: 20px; background: white;
      border: 1px solid #ccc; border-radius: 8px; padding: 16px;
      z-index: 1000000; display: none; box-shadow: 0 4px 12px rgba(0,0,0,0.15);
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; font-size: 14px;
      max-width: 300px;
    `
    selectionPanel.innerHTML = `
      <div style="margin-bottom: 12px; font-weight: 600; color: #333;">网页剪藏 - 选择内容</div>
      <div style="margin-bottom: 8px; color: #666; font-size: 12px;">悬停高亮元素，点击选择内容区域</div>
      <div style="margin-bottom: 12px; color: #666; font-size: 12px;">已选择: <span id="selection-count">0</span> 个区域</div>
      <div style="display: flex; gap: 8px;">
        <button id="confirm-selection" style="background:#007bff;color:white;border:none;border-radius:6px;padding:6px 10px;cursor:pointer">确定</button>
        <button id="clear-selection" style="background:#f3f4f6;color:#111827;border:1px solid #e5e7eb;border-radius:6px;padding:6px 10px;cursor:pointer">清除</button>
        <button id="cancel-selection" style="background:#fef2f2;color:#b91c1c;border:1px solid #fecaca;border-radius:6px;padding:6px 10px;cursor:pointer">取消</button>
      </div>`
    document.body.appendChild(selectionPanel)
    selectionPanel.querySelector('#confirm-selection')?.addEventListener('click', confirmSelection)
    selectionPanel.querySelector('#clear-selection')?.addEventListener('click', () => clearSelectedElements())
    selectionPanel.querySelector('#cancel-selection')?.addEventListener('click', stopSelectionMode)
  }
}

export function startSelectionMode() {
  initSelection()
  isSelectionMode = true
  if (selectionOverlay) selectionOverlay.style.display = 'block'
  if (selectionPanel) selectionPanel.style.display = 'block'
  setupSelectionEventListeners()
}

export function stopSelectionMode() {
  isSelectionMode = false
  if (selectionOverlay) selectionOverlay.style.display = 'none'
  if (selectionPanel) selectionPanel.style.display = 'none'
  clearSelectedElements()
  teardownSelectionEventListeners()
}

function setupSelectionEventListeners() {
  document.addEventListener('mousemove', handleHover, true)
  document.addEventListener('click', handleClick, true)
}

function teardownSelectionEventListeners() {
  document.removeEventListener('mousemove', handleHover, true)
  document.removeEventListener('click', handleClick, true)
}

function handleHover(e: MouseEvent) {
  if (!isSelectionMode) return
  const target = e.target as Element
  if (highlightedElement) {
    (highlightedElement as HTMLElement).style.outline = ''
  }
  highlightedElement = target
  ;(highlightedElement as HTMLElement).style.outline = '2px solid #3b82f6'
}

function handleClick(e: MouseEvent) {
  if (!isSelectionMode) return
  e.preventDefault()
  e.stopPropagation()
  if (highlightedElement) {
    selectedElements.push(highlightedElement)
    ;(highlightedElement as HTMLElement).style.outline = '2px solid #10b981'
    updateSelectionCount()
  }
}

function clearSelectedElements() {
  selectedElements.forEach((el) => ((el as HTMLElement).style.outline = ''))
  selectedElements.length = 0
  updateSelectionCount()
}

function updateSelectionCount() {
  const el = document.getElementById('selection-count')
  if (el) el.textContent = String(selectedElements.length)
}

function confirmSelection() {
  if (selectedElements.length === 0) {
    alert('请至少选择一个内容区域')
    return
  }
  const payload = selectedElements.map((el, idx) => {
    const html = (el as HTMLElement).outerHTML
    return { index: idx + 1, html }
  })
  browser.runtime.sendMessage({ type: 'SELECTED_CONTENT', data: payload })
  stopSelectionMode()
}
