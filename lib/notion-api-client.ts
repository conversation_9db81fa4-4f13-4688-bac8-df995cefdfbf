/**
 * 扩展环境下的Notion API客户端
 * 通过background script发送请求，避免CORS和执行上下文问题
 */

import { NotionConfig } from '@/hooks/use-config'

export interface NotionApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

export interface NotionPage {
  id: string
  url?: string
  properties: Record<string, any>
  created_time: string
  last_edited_time: string
}

export interface NotionDatabase {
  id: string
  title: string
  url?: string
  properties: Record<string, any>
}

export interface NotionUser {
  id: string
  name?: string
  avatar_url?: string
  type: string
  person?: {
    email?: string
  }
}

export interface ClipDataForNotion {
  title: string
  url: string
  content: string
  author?: string | null
  publishDate?: string | null
  coverImage?: string | null
  site: string
  timestamp: string
  tags?: string[]
}

export interface NotionCreateResult {
  success: boolean
  pageId?: string
  pageUrl?: string
  error?: string
}

/**
 * 扩展环境下的Notion API客户端
 */
export class ExtensionNotionClient {
  private config: NotionConfig

  constructor(config: NotionConfig) {
    this.config = config
  }

  /**
   * 通过background script发送API请求
   */
  private async makeApiCall<T = any>(
    endpoint: string,
    method: string = 'GET',
    body?: any
  ): Promise<NotionApiResponse<T>> {
    try {
      console.log('Sending API request:', { endpoint, method, body });

      // 使用Promise包装来处理可能的响应问题
      const response = await new Promise<NotionApiResponse<T>>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('API请求超时'))
        }, 30000) // 30秒超时

        browser.runtime.sendMessage({
          type: 'NOTION_API_CALL',
          endpoint,
          method,
          headers: {
            'Authorization': `Bearer ${this.config.token}`,
          },
          body
        }).then((result) => {
          clearTimeout(timeout)
          console.log('Received API response:', result);
          
          if (!result) {
            reject(new Error('收到空响应，可能是background script未响应'))
            return
          }

          if (typeof result !== 'object') {
            reject(new Error(`收到非对象响应: ${typeof result}`))
            return
          }

          if (!('success' in result)) {
            reject(new Error('响应格式不正确，缺少success字段'))
            return
          }

          resolve(result as NotionApiResponse<T>)
        }).catch((error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })

      return response
    } catch (error) {
      console.error('API call failed:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 测试background script通信
   */
  async testBackgroundScript(): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('测试background script通信...')
      
      const response = await new Promise<any>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Background script响应超时'))
        }, 5000)

        browser.runtime.sendMessage({
          type: 'NOTION_API_CALL',
          endpoint: 'https://httpbin.org/get', // 使用公共测试API
          method: 'GET',
          headers: {}
        }).then((result) => {
          clearTimeout(timeout)
          resolve(result)
        }).catch((error) => {
          clearTimeout(timeout)
          reject(error)
        })
      })

      console.log('Background script响应:', response)
      
      if (!response) {
        return { success: false, error: '收到空响应' }
      }

      if (typeof response !== 'object') {
        return { success: false, error: `收到非对象响应: ${typeof response}` }
      }

      if (!('success' in response)) {
        return { success: false, error: '响应格式不正确，缺少success字段' }
      }

      return { success: response.success, error: response.error }
    } catch (error) {
      console.error('Background script测试失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 测试连接
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      // 首先测试background script通信
      const bgTest = await this.testBackgroundScript()
      if (!bgTest.success) {
        return { success: false, error: `Background script通信失败: ${bgTest.error}` }
      }
      
      // 测试获取用户信息
      const userResult = await this.makeApiCall<NotionUser>('https://api.notion.com/v1/users/me')
      
      if (!userResult.success) {
        return { success: false, error: userResult.error || '获取用户信息失败' }
      }

      // 测试访问数据库
      if (this.config.databaseId) {
        const dbResult = await this.makeApiCall<NotionDatabase>(
          `https://api.notion.com/v1/databases/${this.config.databaseId}`
        )
        
        if (!dbResult.success) {
          return { success: false, error: dbResult.error || '访问数据库失败' }
        }
      }

      return { success: true }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '连接失败'
      return { success: false, error: errorMessage }
    }
  }

  /**
   * 获取数据库信息
   */
  async getDatabaseInfo(): Promise<NotionDatabase | null> {
    if (!this.config.databaseId) {
      return null
    }

    try {
      const result = await this.makeApiCall<NotionDatabase>(
        `https://api.notion.com/v1/databases/${this.config.databaseId}`
      )

      if (result.success && result.data) {
        return {
          id: result.data.id,
          title: this.extractDatabaseTitle(result.data),
          url: result.data.url || `https://notion.so/${result.data.id.replace(/-/g, '')}`,
          properties: result.data.properties
        }
      }

      return null
    } catch (error) {
      console.error('获取数据库信息失败:', error)
      return null
    }
  }

  /**
   * 创建页面
   */
  async createPage(clipData: ClipDataForNotion): Promise<NotionCreateResult> {
    if (!this.config.databaseId) {
      return {
        success: false,
        error: 'Notion客户端或数据库ID未配置'
      }
    }

    try {
      // 构建页面属性
      const properties = this.buildPageProperties(clipData)
      
      // 构建页面内容（异步）
      const allChildren = await this.buildPageContent(clipData)
      
      // 检查 children 数量是否超过 100
      const MAX_CHILDREN_PER_REQUEST = 100
      const initialChildren = allChildren.slice(0, MAX_CHILDREN_PER_REQUEST)
      const remainingChildren = allChildren.slice(MAX_CHILDREN_PER_REQUEST)

      console.log(`总共 ${allChildren.length} 个 children，首次创建 ${initialChildren.length} 个，剩余 ${remainingChildren.length} 个`)

      // 创建页面
      const result = await this.makeApiCall<NotionPage>(
        'https://api.notion.com/v1/pages',
        'POST',
        {
          parent: {
            type: 'database_id',
            database_id: this.config.databaseId
          },
          properties,
          children: initialChildren
        }
      )

      if (result.success && result.data) {
        const pageId = result.data.id
        const pageUrl = result.data.url || `https://notion.so/${pageId.replace(/-/g, '')}`

        // 如果有剩余内容，分批追加
        if (remainingChildren.length > 0) {
          await this.appendRemainingContent(pageId, remainingChildren)
        }

        return {
          success: true,
          pageId,
          pageUrl
        }
      }

      return {
        success: false,
        error: result.error || '创建页面失败'
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '创建页面失败'
      console.error('Notion页面创建失败:', error)
      
      return {
        success: false,
        error: errorMessage
      }
    }
  }

  /**
   * 分批追加剩余内容
   */
  private async appendRemainingContent(pageId: string, children: any[]): Promise<void> {
    const MAX_CHILDREN_PER_REQUEST = 100
    
    for (let i = 0; i < children.length; i += MAX_CHILDREN_PER_REQUEST) {
      const batch = children.slice(i, i + MAX_CHILDREN_PER_REQUEST)
      
      console.log(`追加批次 ${Math.floor(i / MAX_CHILDREN_PER_REQUEST) + 1}，包含 ${batch.length} 个 children`)
      
      try {
        const result = await this.makeApiCall(
          `https://api.notion.com/v1/blocks/${pageId}/children`,
          'PATCH',
          {
            children: batch
          }
        )
        
        if (!result.success) {
          console.error(`追加内容失败 (批次 ${Math.floor(i / MAX_CHILDREN_PER_REQUEST) + 1}):`, result.error)
        }
      } catch (error) {
        console.error(`追加内容失败 (批次 ${Math.floor(i / MAX_CHILDREN_PER_REQUEST) + 1}):`, error)
      }
    }
  }

  /**
   * 构建页面属性
   */
  private buildPageProperties(clipData: ClipDataForNotion): Record<string, any> {
    const properties: Record<string, any> = {}

    // 标题 - 通常数据库都有Title属性
    properties['Title'] = {
      title: [
        {
          text: {
            content: clipData.title
          }
        }
      ]
    }

    // URL
    if (clipData.url) {
      properties['URL'] = {
        url: clipData.url
      }
    }

    // 作者
    if (clipData.author) {
      properties['Author'] = {
        rich_text: [
          {
            text: {
              content: clipData.author
            }
          }
        ]
      }
    }

    // 发布日期
    if (clipData.publishDate) {
      try {
        const date = new Date(clipData.publishDate)
        if (!isNaN(date.getTime())) {
          properties['Published'] = {
            date: {
              start: date.toISOString().split('T')[0]
            }
          }
        }
      } catch (error) {
        console.warn('发布日期格式错误:', clipData.publishDate)
      }
    }

    // 网站来源
    if (clipData.site) {
      properties['Source'] = {
        rich_text: [
          {
            text: {
              content: clipData.site
            }
          }
        ]
      }
    }

    // 剪藏时间
    try {
      const clippedDate = new Date(clipData.timestamp)
      if (!isNaN(clippedDate.getTime())) {
        properties['Clipped'] = {
          date: {
            start: clippedDate.toISOString()
          }
        }
      }
    } catch (error) {
      console.warn('剪藏时间格式错误:', clipData.timestamp)
    }

    // 标签
    if (clipData.tags && clipData.tags.length > 0) {
      properties['Tags'] = {
        multi_select: clipData.tags.map(tag => ({ name: tag }))
      }
    }

    // 封面图片
    if (clipData.coverImage) {
      properties['Cover'] = {
        files: [
          {
            name: 'cover.jpg',
            external: {
              url: clipData.coverImage
            }
          }
        ]
      }
    }

    return properties
  }

  /**
   * 构建页面内容
   */
  private async buildPageContent(clipData: ClipDataForNotion): Promise<any[]> {
    const children: any[] = []

    // 添加封面图片（如果有）
    if (clipData.coverImage) {
      children.push({
        object: 'block',
        type: 'image',
        image: {
          type: 'external',
          external: {
            url: clipData.coverImage
          }
        }
      })
    }

    // 添加元信息段落
    const metaInfo = []
    if (clipData.author) metaInfo.push(`👤 ${clipData.author}`)
    if (clipData.publishDate) metaInfo.push(`📅 ${clipData.publishDate}`)
    if (clipData.site) metaInfo.push(`🌐 ${clipData.site}`)
    
    if (metaInfo.length > 0) {
      children.push({
        object: 'block',
        type: 'paragraph',
        paragraph: {
          rich_text: [
            {
              text: {
                content: metaInfo.join(' • ')
              }
            }
          ]
        }
      })
    }

    // 添加原文链接
    children.push({
      object: 'block',
      type: 'paragraph',
      paragraph: {
        rich_text: [
          {
            text: {
              content: '🔗 原文链接: '
            }
          },
          {
            text: {
              content: clipData.url,
              link: {
                url: clipData.url
              }
            }
          }
        ]
      }
    })

    // 添加分隔线
    children.push({
      object: 'block',
      type: 'divider',
      divider: {}
    })

    // 使用专门的Markdown转换器将内容转换为Notion blocks
    const contentBlocks = await this.convertMarkdownToNotionBlocks(clipData.content)
    children.push(...contentBlocks)

    return children
  }

  /**
   * 使用专门的转换器将Markdown转换为Notion blocks
   */
  private async convertMarkdownToNotionBlocks(markdown: string): Promise<any[]> {
    try {
      // 动态导入转换器以减小初始包大小
      const { createMarkdownToNotionConverter } = await import('@/lib/markdown-to-notion')
      const converter = createMarkdownToNotionConverter()
      
      console.log('开始转换Markdown到Notion blocks...')
      console.log('原始Markdown长度:', markdown.length)
      
      const blocks = converter.convert(markdown)
      
      console.log('转换完成，生成了', blocks.length, '个blocks')
      console.log('转换后的blocks预览:', blocks.slice(0, 3))
      
      return blocks
    } catch (error) {
      console.error('Markdown转换失败，使用简化版本:', error)
      // 如果转换失败，回退到简化版本
      return this.convertMarkdownToBlocksSimple(markdown)
    }
  }

  /**
   * 简化版Markdown转换（作为回退方案）
   */
  private convertMarkdownToBlocksSimple(markdown: string): any[] {
    const blocks: any[] = []
    const lines = markdown.split('\n')
    let currentParagraph = ''

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 空行处理
      if (!trimmedLine) {
        if (currentParagraph) {
          blocks.push(this.createParagraphBlock(currentParagraph))
          currentParagraph = ''
        }
        continue
      }

      // 标题处理
      if (trimmedLine.startsWith('#')) {
        if (currentParagraph) {
          blocks.push(this.createParagraphBlock(currentParagraph))
          currentParagraph = ''
        }

        const level = trimmedLine.match(/^#+/)?.[0].length || 1
        const title = trimmedLine.replace(/^#+\s*/, '')
        
        blocks.push(this.createHeadingBlock(title, Math.min(level, 3)))
        continue
      }

      // 普通段落累积
      if (currentParagraph) {
        currentParagraph += ' ' + trimmedLine
      } else {
        currentParagraph = trimmedLine
      }
    }

    // 处理最后一个段落
    if (currentParagraph) {
      blocks.push(this.createParagraphBlock(currentParagraph))
    }

    return blocks
  }

  /**
   * 转换Markdown为Notion blocks（简化版）
   */
  private convertMarkdownToBlocks(markdown: string): any[] {
    const blocks: any[] = []
    const lines = markdown.split('\n')
    let currentParagraph = ''

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 空行处理
      if (!trimmedLine) {
        if (currentParagraph) {
          blocks.push(this.createParagraphBlock(currentParagraph))
          currentParagraph = ''
        }
        continue
      }

      // 标题处理
      if (trimmedLine.startsWith('#')) {
        if (currentParagraph) {
          blocks.push(this.createParagraphBlock(currentParagraph))
          currentParagraph = ''
        }

        const level = trimmedLine.match(/^#+/)?.[0].length || 1
        const title = trimmedLine.replace(/^#+\s*/, '')
        
        blocks.push(this.createHeadingBlock(title, Math.min(level, 3)))
        continue
      }

      // 普通段落累积
      if (currentParagraph) {
        currentParagraph += ' ' + trimmedLine
      } else {
        currentParagraph = trimmedLine
      }
    }

    // 处理最后一个段落
    if (currentParagraph) {
      blocks.push(this.createParagraphBlock(currentParagraph))
    }

    return blocks
  }

  /**
   * 创建段落块
   */
  private createParagraphBlock(text: string): any {
    return {
      object: 'block',
      type: 'paragraph',
      paragraph: {
        rich_text: [
          {
            text: {
              content: text
            }
          }
        ]
      }
    }
  }

  /**
   * 创建标题块
   */
  private createHeadingBlock(text: string, level: number): any {
    const headingType = `heading_${level}` as 'heading_1' | 'heading_2' | 'heading_3'
    
    return {
      object: 'block',
      type: headingType,
      [headingType]: {
        rich_text: [
          {
            text: {
              content: text
            }
          }
        ]
      }
    }
  }

  /**
   * 提取数据库标题
   */
  private extractDatabaseTitle(database: any): string {
    if (database.title && database.title.length > 0) {
      return database.title[0].plain_text || 'Untitled Database'
    }
    return 'Untitled Database'
  }
}

/**
 * 创建扩展环境下的Notion客户端实例
 */
export function createExtensionNotionClient(config: NotionConfig): ExtensionNotionClient {
  return new ExtensionNotionClient(config)
}