/**
 * Markdown to Notion Blocks 转换器
 * 将Markdown文本转换为Notion API支持的blocks格式
 */

export interface NotionBlock {
  object: 'block'
  type: string
  [key: string]: any
}

export interface NotionRichText {
  type: 'text'
  text: {
    content: string
    link?: {
      url: string
    }
  }
  annotations?: {
    bold?: boolean
    italic?: boolean
    strikethrough?: boolean
    underline?: boolean
    code?: boolean
    color?: string
  }
  plain_text?: string
  href?: string
}

/**
 * Markdown到Notion Blocks转换器
 */
export class MarkdownToNotionConverter {
  
  /**
   * 将Markdown文本转换为Notion blocks
   */
  convert(markdown: string): NotionBlock[] {
    const blocks: NotionBlock[] = []
    const lines = this.preprocessLines(markdown)
    
    let i = 0
    while (i < lines.length) {
      const line = lines[i]
      const trimmed = line.trim()
      
      // 跳过空行
      if (!trimmed) {
        i++
        continue
      }
      
      // 处理代码块
      if (trimmed.startsWith('```')) {
        const codeBlockResult = this.parseCodeBlock(lines, i)
        blocks.push(codeBlockResult.block)
        i = codeBlockResult.nextIndex
        continue
      }
      
      // 处理标题
      if (trimmed.startsWith('#')) {
        blocks.push(this.parseHeading(trimmed))
        i++
        continue
      }
      
      // 处理引用
      if (trimmed.startsWith('>')) {
        const quoteResult = this.parseQuote(lines, i)
        blocks.push(quoteResult.block)
        i = quoteResult.nextIndex
        continue
      }
      
      // 处理无序列表
      if (this.isUnorderedListItem(trimmed)) {
        const listResult = this.parseUnorderedList(lines, i)
        blocks.push(...listResult.blocks)
        i = listResult.nextIndex
        continue
      }
      
      // 处理有序列表
      if (this.isOrderedListItem(trimmed)) {
        const listResult = this.parseOrderedList(lines, i)
        blocks.push(...listResult.blocks)
        i = listResult.nextIndex
        continue
      }
      
      // 处理表格
      if (this.isTableRow(trimmed) && i + 1 < lines.length && this.isTableSeparator(lines[i + 1].trim())) {
        const tableResult = this.parseTable(lines, i)
        blocks.push(tableResult.block)
        i = tableResult.nextIndex
        continue
      }
      
      // 处理分割线
      if (this.isDivider(trimmed)) {
        blocks.push(this.createDividerBlock())
        i++
        continue
      }
      
      // 处理普通段落
      const paragraphResult = this.parseParagraph(lines, i)
      if (paragraphResult.block) {
        blocks.push(paragraphResult.block)
      }
      
      // 特殊处理：如果段落包含多个图片，需要额外处理
      const content = lines.slice(i, paragraphResult.nextIndex).join(' ').trim()
      const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g
      const images = Array.from(content.matchAll(imageRegex))
      
      if (images.length > 1) {
        const textWithoutImages = content.replace(imageRegex, '').trim()
        if (!textWithoutImages) {
          // 如果只有图片，为每个图片创建单独的块
          blocks.pop() // 移除刚才添加的块
          for (const image of images) {
            const [, alt, url] = image
            blocks.push({
              object: 'block',
              type: 'image',
              image: {
                type: 'external',
                external: { url },
                caption: alt ? this.parseInlineFormatting(alt) : []
              }
            })
          }
        }
      }
      
      i = paragraphResult.nextIndex
    }
    
    return blocks
  }
  
  /**
   * 预处理行，合并多行段落等
   */
  private preprocessLines(markdown: string): string[] {
    return markdown.split('\n').map(line => line.replace(/\r$/g, ''))
  }
  
  /**
   * 解析标题
   */
  private parseHeading(line: string): NotionBlock {
    const match = line.match(/^(#{1,6})\s+(.+)$/)
    if (!match) {
      return this.createParagraphBlock(line)
    }
    
    const level = Math.min(match[1].length, 3) // Notion只支持1-3级标题
    const text = match[2]
    const headingType = `heading_${level}` as 'heading_1' | 'heading_2' | 'heading_3'
    
    return {
      object: 'block',
      type: headingType,
      [headingType]: {
        rich_text: this.parseInlineFormatting(text)
      }
    }
  }
  
  /**
   * 解析代码块
   */
  private parseCodeBlock(lines: string[], startIndex: number): { block: NotionBlock; nextIndex: number } {
    const firstLine = lines[startIndex].trim()
    const languageMatch = firstLine.match(/^```(\w+)?/)
    const language = languageMatch?.[1] || 'plain text'
    
    const codeLines: string[] = []
    let i = startIndex + 1
    
    // 查找结束标记
    while (i < lines.length) {
      const line = lines[i]
      if (line.trim() === '```') {
        break
      }
      codeLines.push(line)
      i++
    }
    
    const code = codeLines.join('\n')
    
    return {
      block: {
        object: 'block',
        type: 'code',
        code: {
          caption: [],
          rich_text: [{
            type: 'text',
            text: { content: code }
          }],
          language: this.mapLanguage(language)
        }
      },
      nextIndex: i + 1
    }
  }
  
  /**
   * 解析引用块
   */
  private parseQuote(lines: string[], startIndex: number): { block: NotionBlock; nextIndex: number } {
    const quoteLines: string[] = []
    let i = startIndex
    
    // 收集连续的引用行
    while (i < lines.length) {
      const line = lines[i].trim()
      if (!line.startsWith('>')) {
        break
      }
      quoteLines.push(line.replace(/^>\s?/, ''))
      i++
    }
    
    const quoteText = quoteLines.join('\n').trim()
    
    return {
      block: {
        object: 'block',
        type: 'quote',
        quote: {
          rich_text: this.parseInlineFormatting(quoteText)
        }
      },
      nextIndex: i
    }
  }
  
  /**
   * 解析无序列表
   */
  private parseUnorderedList(lines: string[], startIndex: number): { blocks: NotionBlock[]; nextIndex: number } {
    const blocks: NotionBlock[] = []
    let i = startIndex
    
    while (i < lines.length) {
      const line = lines[i].trim()
      if (!this.isUnorderedListItem(line)) {
        break
      }
      
      const content = line.replace(/^[-*+]\s+/, '')
      const indent = this.getIndentLevel(lines[i])
      
      blocks.push({
        object: 'block',
        type: 'bulleted_list_item',
        bulleted_list_item: {
          rich_text: this.parseInlineFormatting(content)
        }
      })
      
      i++
    }
    
    return { blocks, nextIndex: i }
  }
  
  /**
   * 解析有序列表
   */
  private parseOrderedList(lines: string[], startIndex: number): { blocks: NotionBlock[]; nextIndex: number } {
    const blocks: NotionBlock[] = []
    let i = startIndex
    
    while (i < lines.length) {
      const line = lines[i].trim()
      if (!this.isOrderedListItem(line)) {
        break
      }
      
      const content = line.replace(/^\d+\.\s+/, '')
      
      blocks.push({
        object: 'block',
        type: 'numbered_list_item',
        numbered_list_item: {
          rich_text: this.parseInlineFormatting(content)
        }
      })
      
      i++
    }
    
    return { blocks, nextIndex: i }
  }
  
  /**
   * 解析表格
   */
  private parseTable(lines: string[], startIndex: number): { block: NotionBlock; nextIndex: number } {
    const tableRows: string[][] = []
    let i = startIndex
    
    // 解析表头
    const headerLine = lines[i].trim()
    const headers = this.parseTableRow(headerLine)
    tableRows.push(headers)
    i += 2 // 跳过分隔符行
    
    // 解析表格内容
    while (i < lines.length) {
      const line = lines[i].trim()
      if (!this.isTableRow(line)) {
        break
      }
      const row = this.parseTableRow(line)
      tableRows.push(row)
      i++
    }
    
    // 创建表格块
    const tableBlock: NotionBlock = {
      object: 'block',
      type: 'table',
      table: {
        table_width: headers.length,
        has_column_header: true,
        has_row_header: false
      }
    }
    
    // 注意: 实际的表格内容需要通过children属性添加
    // 这里我们简化处理，将表格转换为段落
    const tableContent = tableRows.map(row => row.join(' | ')).join('\n')
    
    return {
      block: {
        object: 'block',
        type: 'paragraph',
        paragraph: {
          rich_text: [{
            type: 'text',
            text: { content: `表格:\n${tableContent}` }
          }]
        }
      },
      nextIndex: i
    }
  }
  
  /**
   * 解析段落
   */
  private parseParagraph(lines: string[], startIndex: number): { block: NotionBlock | null; nextIndex: number } {
    const paragraphLines: string[] = []
    let i = startIndex
    
    // 收集连续的非空行作为段落
    while (i < lines.length) {
      const line = lines[i].trim()
      
      // 遇到空行或特殊格式行就停止
      if (!line || 
          line.startsWith('#') || 
          line.startsWith('>') || 
          line.startsWith('```') ||
          this.isUnorderedListItem(line) ||
          this.isOrderedListItem(line) ||
          this.isDivider(line)) {
        break
      }
      
      paragraphLines.push(line)
      i++
    }
    
    if (paragraphLines.length === 0) {
      return { block: null, nextIndex: i }
    }
    
    const content = paragraphLines.join(' ').trim()
    
    // 处理图片
    const imageMatch = content.match(/^!\[([^\]]*)\]\(([^)]+)\)$/)
    if (imageMatch) {
      const [, alt, url] = imageMatch
      return {
        block: {
          object: 'block',
          type: 'image',
          image: {
            type: 'external',
            external: { url },
            caption: alt ? this.parseInlineFormatting(alt) : []
          }
        },
        nextIndex: i
      }
    }
    
    // 检查段落中是否包含图片（支持多个图片）
    const imageRegex = /!\[([^\]]*)\]\(([^)]+)\)/g
    const images = Array.from(content.matchAll(imageRegex))
    
    if (images.length > 0) {
      // 如果段落只包含图片，将每个图片作为单独的块
      const textWithoutImages = content.replace(imageRegex, '').trim()
      
      if (!textWithoutImages) {
        // 只有图片，创建多个图片块
        const imageBlocks: NotionBlock[] = []
        for (const image of images) {
          const [, alt, url] = image
          imageBlocks.push({
            object: 'block',
            type: 'image',
            image: {
              type: 'external',
              external: { url },
              caption: alt ? this.parseInlineFormatting(alt) : []
            }
          })
        }
        return {
          block: imageBlocks.length === 1 ? imageBlocks[0] : null, // 返回第一个图片块，其他的需要特殊处理
          nextIndex: i
        }
      }
    }
    
    return {
      block: this.createParagraphBlock(content),
      nextIndex: i
    }
  }
  
  /**
   * 创建段落块
   */
  private createParagraphBlock(content: string): NotionBlock {
    return {
      object: 'block',
      type: 'paragraph',
      paragraph: {
        rich_text: this.parseInlineFormatting(content)
      }
    }
  }
  
  /**
   * 创建分割线块
   */
  private createDividerBlock(): NotionBlock {
    return {
      object: 'block',
      type: 'divider',
      divider: {}
    }
  }
  
  /**
   * 解析行内格式化（粗体、斜体、链接等）
   */
  private parseInlineFormatting(text: string): NotionRichText[] {
    if (!text.trim()) {
      return []
    }
    
    const richTexts: NotionRichText[] = []
    let currentPos = 0
    
    // 使用正则表达式匹配各种格式
    const patterns = [
      // 链接 [text](url)
      { regex: /\[([^\]]+)\]\(([^)]+)\)/g, type: 'link' },
      // 粗体 **text** 或 __text__
      { regex: /\*\*([^*]+)\*\*/g, type: 'bold' },
      { regex: /__([^_]+)__/g, type: 'bold' },
      // 斜体 *text* 或 _text_
      { regex: /\*([^*]+)\*/g, type: 'italic' },
      { regex: /_([^_]+)_/g, type: 'italic' },
      // 行内代码 `code`
      { regex: /`([^`]+)`/g, type: 'code' },
      // 删除线 ~~text~~
      { regex: /~~([^~]+)~~/g, type: 'strikethrough' }
    ]
    
    // 找到所有匹配项
    const matches: Array<{
      start: number
      end: number
      type: string
      content: string
      url?: string
    }> = []
    
    for (const pattern of patterns) {
      pattern.regex.lastIndex = 0
      let match
      while ((match = pattern.regex.exec(text)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          type: pattern.type,
          content: match[1],
          url: pattern.type === 'link' ? match[2] : undefined
        })
      }
    }
    
    // 按位置排序
    matches.sort((a, b) => a.start - b.start)
    
    // 处理重叠和嵌套（简化处理：忽略重叠）
    const validMatches = []
    let lastEnd = 0
    for (const match of matches) {
      if (match.start >= lastEnd) {
        validMatches.push(match)
        lastEnd = match.end
      }
    }
    
    // 构建rich text数组
    lastEnd = 0
    for (const match of validMatches) {
      // 添加匹配前的普通文本
      if (match.start > lastEnd) {
        const plainText = text.slice(lastEnd, match.start)
        if (plainText) {
          richTexts.push({
            type: 'text',
            text: { content: plainText }
          })
        }
      }
      
      // 添加格式化文本
      const richText: NotionRichText = {
        type: 'text',
        text: { content: match.content }
      }
      
      if (match.type === 'link') {
        richText.text.link = { url: match.url! }
      } else {
        richText.annotations = {}
        switch (match.type) {
          case 'bold':
            richText.annotations.bold = true
            break
          case 'italic':
            richText.annotations.italic = true
            break
          case 'code':
            richText.annotations.code = true
            break
          case 'strikethrough':
            richText.annotations.strikethrough = true
            break
        }
      }
      
      richTexts.push(richText)
      lastEnd = match.end
    }
    
    // 添加剩余的普通文本
    if (lastEnd < text.length) {
      const plainText = text.slice(lastEnd)
      if (plainText) {
        richTexts.push({
          type: 'text',
          text: { content: plainText }
        })
      }
    }
    
    // 如果没有任何格式化，返回普通文本
    if (richTexts.length === 0) {
      richTexts.push({
        type: 'text',
        text: { content: text }
      })
    }
    
    return richTexts
  }
  
  /**
   * 辅助方法：判断是否为无序列表项
   */
  private isUnorderedListItem(line: string): boolean {
    return /^[-*+]\s+/.test(line)
  }
  
  /**
   * 辅助方法：判断是否为有序列表项
   */
  private isOrderedListItem(line: string): boolean {
    return /^\d+\.\s+/.test(line)
  }
  
  /**
   * 辅助方法：判断是否为表格行
   */
  private isTableRow(line: string): boolean {
    return line.includes('|') && line.trim().length > 1
  }
  
  /**
   * 辅助方法：判断是否为表格分隔符
   */
  private isTableSeparator(line: string): boolean {
    return /^[\s\|:\-]+$/.test(line)
  }
  
  /**
   * 辅助方法：判断是否为分割线
   */
  private isDivider(line: string): boolean {
    return /^[-*_]{3,}$/.test(line.replace(/\s/g, ''))
  }
  
  /**
   * 辅助方法：解析表格行
   */
  private parseTableRow(line: string): string[] {
    return line.split('|').map(cell => cell.trim()).filter(cell => cell !== '')
  }
  
  /**
   * 辅助方法：获取缩进级别
   */
  private getIndentLevel(line: string): number {
    const match = line.match(/^(\s*)/)
    return match ? Math.floor(match[1].length / 2) : 0
  }
  
  /**
   * 映射语言代码到Notion支持的语言
   */
  private mapLanguage(language: string): string {
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'jsx': 'javascript',
      'tsx': 'typescript',
      'py': 'python',
      'rb': 'ruby',
      'sh': 'shell',
      'bash': 'shell',
      'zsh': 'shell',
      'yml': 'yaml',
      'md': 'markdown'
    }
    
    return languageMap[language.toLowerCase()] || language.toLowerCase()
  }
}

/**
 * 创建Markdown到Notion转换器实例
 */
export function createMarkdownToNotionConverter(): MarkdownToNotionConverter {
  return new MarkdownToNotionConverter()
}

/**
 * 测试转换器功能的示例
 */
export function testMarkdownConversion(): void {
  const converter = createMarkdownToNotionConverter()
  
  const testMarkdown = `# 测试标题

这是一个段落，包含**粗体文字**和*斜体文字*。

## 二级标题

这里有一个列表：
- 第一项
- 第二项
- 第三项

还有有序列表：
1. 首先
2. 然后
3. 最后

这是一个[链接](https://example.com)和一些\`行内代码\`。

> 这是一个引用块
> 可以有多行

\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

---

最后是一个分割线和图片：

![示例图片](https://example.com/image.jpg)
`

  const blocks = converter.convert(testMarkdown)
  console.log('转换测试完成，生成blocks:', blocks.length)
  console.log('第一个block:', JSON.stringify(blocks[0], null, 2))
}