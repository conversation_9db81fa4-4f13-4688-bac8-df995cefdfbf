/**
 * AI辅助内容提取服务
 * 使用大模型分析网页DOM结构，智能定位正文内容
 */

import { AIConfig } from '@/hooks/use-config'
import { askModel } from '@/lib/llm'

export interface DOMElementInfo {
  selector: string
  tagName: string
  className: string
  id: string
  textLength: number
  textPreview: string
  hasImages: boolean
  hasLinks: boolean
  childCount: number
  depth: number
}

export interface AIExtractionResult {
  success: boolean
  selectors: string[]
  reasoning: string
  confidence: number
  error?: string
}

export interface AIAnalysisRequest {
  url: string
  title: string
  domElements: DOMElementInfo[]
  pageStructure: string
}

/**
 * AI内容提取器类
 */
export class AIContentExtractor {
  constructor(private aiConfig: AIConfig) {}

  /**
   * 分析页面DOM结构，使用AI推荐正文元素选择器
   */
  async analyzePageStructure(request: AIAnalysisRequest): Promise<AIExtractionResult> {
    try {
      if (!this.aiConfig.enabled || !this.aiConfig.apiKey) {
        throw new Error('AI服务未启用或API密钥未配置')
      }

      const prompt = this.buildAnalysisPrompt(request)
      const response = await this.callAIService(prompt)
      
      return this.parseAIResponse(response)
    } catch (error) {
      console.error('AI内容提取失败:', error)
      return {
        success: false,
        selectors: [],
        reasoning: '',
        confidence: 0,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 构建AI分析提示词
   */
  private buildAnalysisPrompt(request: AIAnalysisRequest): string {
    const { url, title, domElements, pageStructure } = request

    return `你是一个专业的网页内容提取专家。请分析以下网页的DOM结构，识别并推荐包含主要正文内容的HTML元素选择器。

网页信息：
- URL: ${url}
- 标题: ${title}
- 页面结构概览: ${pageStructure}

DOM元素分析数据：
${domElements.map(el => `
- 选择器: ${el.selector}
- 标签: ${el.tagName}
- 类名: ${el.className}
- ID: ${el.id}
- 文本长度: ${el.textLength}
- 文本预览: ${el.textPreview}
- 包含图片: ${el.hasImages}
- 包含链接: ${el.hasLinks}
- 子元素数量: ${el.childCount}
- DOM深度: ${el.depth}
`).join('\n')}

分析要求：
1. 识别最可能包含文章主要内容的元素
2. 排除导航栏、侧边栏、页脚、广告、评论等非正文内容
3. 优先选择语义化标签（article、main等）
4. 考虑文本密度、长度和结构合理性
5. 提供1-3个最佳选择器建议，按优先级排序

请以JSON格式回应：
{
  "selectors": ["选择器1", "选择器2", "选择器3"],
  "reasoning": "选择这些选择器的详细理由",
  "confidence": 0.85
}

注意：
- selectors数组包含推荐的CSS选择器，按优先级排序
- reasoning说明选择的理由和分析过程
- confidence是置信度（0-1之间的数值）
- 只返回JSON，不要包含其他文本`
  }

  /**
   * 调用AI服务
   */
  private async callAIService(prompt: string): Promise<string> {
    try {
      console.log('🤖 通过 LangChain 调用AI服务:', {
        provider: this.aiConfig.provider,
        model: this.aiConfig.model,
        promptLength: prompt.length
      })

      const content = await askModel(this.aiConfig, prompt)
      
      console.log('✅ AI服务调用成功:', {
        responseLength: content.length,
        preview: content.substring(0, 100) + (content.length > 100 ? '...' : '')
      })

      return content
    } catch (error) {
      console.error('❌ AI服务调用失败:', error)
      throw error
    }
  }

  /**
   * 解析AI响应
   */
  private parseAIResponse(response: string): AIExtractionResult {
    try {
      // 尝试严格解析JSON（优先整串解析，其次提取第一个花括号块）
      const tryParse = (text: string): any => {
        try { return JSON.parse(text) } catch { return null }
      }
      let parsed = tryParse(response)
      if (!parsed) {
        const jsonMatch = response.match(/\{[\s\S]*\}/)
        if (!jsonMatch) {
          throw new Error('响应中未找到有效的JSON')
        }
        parsed = tryParse(jsonMatch[0])
        if (!parsed) throw new Error('响应JSON解析失败')
      }
      
      // 验证响应格式
      if (!Array.isArray(parsed.selectors)) {
        throw new Error('响应格式错误：selectors应为数组')
      }

      if (typeof parsed.reasoning !== 'string') {
        throw new Error('响应格式错误：reasoning应为字符串')
      }

      if (typeof parsed.confidence !== 'number') {
        throw new Error('响应格式错误：confidence应为数字')
      }

      return {
        success: true,
        selectors: parsed.selectors,
        reasoning: parsed.reasoning,
        confidence: parsed.confidence
      }
    } catch (error) {
      console.error('解析AI响应失败:', error, '原始响应:', response)
      return {
        success: false,
        selectors: [],
        reasoning: '',
        confidence: 0,
        error: `响应解析失败: ${error instanceof Error ? error.message : '未知错误'}`
      }
    }
  }
}

/**
 * 在页面中收集DOM元素信息用于AI分析
 */
export function collectDOMElementsInfo(): DOMElementInfo[] {
  const elements: DOMElementInfo[] = []
  const visitedElements = new Set<Element>()
  
  // 候选元素选择器
  const candidateSelectors = [
    'article', 'main', 'section', 'div',
    '[role="main"]', '.content', '.post', '.article',
    '.entry', '.text', '.body', '.main'
  ]

  function getElementDepth(element: Element): number {
    let depth = 0
    let current = element
    while (current.parentElement) {
      depth++
      current = current.parentElement
    }
    return depth
  }

  function generateSelector(element: Element): string {
    // 优先使用ID
    if (element.id) {
      return `#${element.id}`
    }
    
    // 使用类名
    if (element.className && typeof element.className === 'string') {
      const firstClass = element.className.split(' ')[0]
      if (firstClass) {
        return `.${firstClass}`
      }
    }
    
    // 使用标签名
    return element.tagName.toLowerCase()
  }

  function analyzeElement(element: Element) {
    if (visitedElements.has(element)) return
    visitedElements.add(element)

    const textContent = element.textContent?.trim() || ''
    const textLength = textContent.length
    
    // 只分析有足够文本内容的元素
    if (textLength < 50) return

    const images = element.querySelectorAll('img')
    const links = element.querySelectorAll('a')
    const children = element.children

    elements.push({
      selector: generateSelector(element),
      tagName: element.tagName.toLowerCase(),
      className: element.className || '',
      id: element.id || '',
      textLength,
      textPreview: textContent.substring(0, 200) + (textContent.length > 200 ? '...' : ''),
      hasImages: images.length > 0,
      hasLinks: links.length > 0,
      childCount: children.length,
      depth: getElementDepth(element)
    })
  }

  // 分析候选元素
  candidateSelectors.forEach(selector => {
    const candidateElements = document.querySelectorAll(selector)
    candidateElements.forEach(analyzeElement)
  })

  // 按文本长度排序，取前20个最相关的元素
  return elements
    .sort((a, b) => b.textLength - a.textLength)
    .slice(0, 20)
}

/**
 * 生成页面结构概览
 */
export function generatePageStructure(): string {
  const structure = []
  
  // 基本信息
  structure.push(`文档类型: ${document.doctype?.name || 'unknown'}`)
  structure.push(`页面语言: ${document.documentElement.lang || 'unknown'}`)
  
  // 关键元素检测
  const hasArticle = document.querySelector('article') !== null
  const hasMain = document.querySelector('main') !== null
  const hasAside = document.querySelector('aside') !== null
  const hasNav = document.querySelector('nav') !== null
  const hasHeader = document.querySelector('header') !== null
  const hasFooter = document.querySelector('footer') !== null
  
  structure.push(`包含语义化标签: article=${hasArticle}, main=${hasMain}, aside=${hasAside}, nav=${hasNav}, header=${hasHeader}, footer=${hasFooter}`)
  
  // 内容区域检测
  const contentSelectors = ['.content', '.post', '.article', '.entry', '.main', '.text']
  const foundContentAreas = contentSelectors.filter(selector => 
    document.querySelector(selector) !== null
  )
  
  if (foundContentAreas.length > 0) {
    structure.push(`常见内容区域类名: ${foundContentAreas.join(', ')}`)
  }
  
  return structure.join('\n')
}