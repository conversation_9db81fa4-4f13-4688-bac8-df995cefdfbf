/**
 * LangChain.js 适配层
 * 统一多个大模型提供商的调用接口
 */

import { ChatOpenAI } from "@langchain/openai"
import { ChatAnthropic } from "@langchain/anthropic"
import { HumanMessage } from "@langchain/core/messages"
import type { AIConfig } from "@/hooks/use-config"

/**
 * 创建聊天模型实例
 */
export function createChatModel(config: AIConfig) {
  const { provider, apiKey, baseUrl, model, temperature, maxTokens } = config

  // 通用配置
  const commonConfig = {
    temperature,
    maxTokens: maxTokens as number,
  }

  // Claude 使用专用适配器
  if (provider === 'claude') {
    return new ChatAnthropic({
      apiKey,
      model,
      ...commonConfig,
    })
  }

  // OpenAI 兼容模型（包括 OpenAI、DeepSeek、Kimi、OpenRouter、Custom）
  const openaiConfig: any = {
    apiKey,
    model,
    ...commonConfig,
  }

  // 设置自定义 baseURL（如果提供）
  if (baseUrl) {
    // 规范化 URL，确保以适当的路径结尾
    let normalizedUrl = baseUrl.trim()
    
    // 添加协议前缀（如果缺失）
    if (!normalizedUrl.startsWith('http://') && !normalizedUrl.startsWith('https://')) {
      normalizedUrl = `https://${normalizedUrl}`
    }
    
    // 确保以 /v1/chat/completions 结尾（OpenAI 兼容端点）
    if (!normalizedUrl.includes('/v1/')) {
      if (normalizedUrl.endsWith('/')) {
        normalizedUrl += 'v1/chat/completions'
      } else {
        normalizedUrl += '/v1/chat/completions'
      }
    }
    
    openaiConfig.baseURL = normalizedUrl
  } else {
    // 使用默认端点
    switch (provider) {
      case 'deepseek':
        openaiConfig.baseURL = 'https://api.deepseek.com/v1/chat/completions'
        break
      case 'kimi':
        openaiConfig.baseURL = 'https://api.moonshot.cn/v1/chat/completions'
        break
      case 'openrouter':
        openaiConfig.baseURL = 'https://openrouter.ai/api/v1/chat/completions'
        break
      case 'openai':
      default:
        // 使用 LangChain 默认的 OpenAI 端点
        break
    }
  }

  // OpenRouter 需要额外的请求头
  if (provider === 'openrouter') {
    openaiConfig.defaultHeaders = {
      'HTTP-Referer': 'https://web-clipper-extension.local',
      'X-Title': 'Web Clipper Extension'
    }
  }

  return new ChatOpenAI(openaiConfig)
}

/**
 * 调用模型获取响应
 */
export async function askModel(config: AIConfig, prompt: string): Promise<string> {
  try {
    console.log('🤖 使用 LangChain 调用模型:', {
      provider: config.provider,
      model: config.model,
      promptLength: prompt.length
    })

    const model = createChatModel(config)
    const message = new HumanMessage(prompt)
    const response = await model.invoke([message])

    // 提取响应内容
    let content = ''
    if (typeof response.content === 'string') {
      content = response.content
    } else if (Array.isArray(response.content)) {
      // 处理复合内容（Claude 可能返回数组）
      content = response.content
        .map((item: any) => {
          if (typeof item === 'string') return item
          if (item.text) return item.text
          if (item.content) return item.content
          return ''
        })
        .join('')
    } else if (response.content && typeof response.content === 'object') {
      // 处理对象形式的内容
      content = (response.content as any).text || (response.content as any).content || ''
    }

    if (!content || content.trim().length === 0) {
      throw new Error('模型返回空响应')
    }

    console.log('✅ LangChain 调用成功:', {
      responseLength: content.length,
      preview: content.substring(0, 100) + (content.length > 100 ? '...' : '')
    })

    return content

  } catch (error) {
    console.error('❌ LangChain 调用失败:', error)
    
    // 提供更友好的错误信息
    if (error instanceof Error) {
      if (error.message.includes('401')) {
        throw new Error('API Key 无效或已过期')
      } else if (error.message.includes('403')) {
        throw new Error('API 访问被拒绝，请检查权限设置')
      } else if (error.message.includes('404')) {
        throw new Error('API 地址不正确或模型不存在')
      } else if (error.message.includes('429')) {
        throw new Error('请求频率过高，请稍后重试')
      } else if (error.message.includes('timeout')) {
        throw new Error('请求超时，请检查网络连接')
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        throw new Error('网络连接失败，请检查网络设置')
      }
    }
    
    throw error
  }
}

/**
 * 测试模型连接
 */
export async function testModelConnection(config: AIConfig): Promise<{ success: boolean; message?: string; error?: string }> {
  try {
    if (!config.enabled) {
      return { success: false, error: 'AI 服务未启用' }
    }

    if (!config.apiKey) {
      return { success: false, error: '请先配置 API Key' }
    }

    if (!config.model) {
      return { success: false, error: '请先配置模型名称' }
    }

    // 发送测试消息
    const testPrompt = "请回复一个简单的测试消息：Hello"
    const response = await askModel(config, testPrompt)
    
    return {
      success: true,
      message: `连接成功！AI 响应: ${response.substring(0, 50)}${response.length > 50 ? '...' : ''}`
    }

  } catch (error) {
    console.error('AI 连接测试失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '连接测试失败'
    }
  }
}

/**
 * 获取支持的模型提供商列表
 */
export function getSupportedProviders(): Array<{ value: string; label: string; description: string }> {
  return [
    { value: 'openai', label: 'OpenAI', description: 'GPT-3.5/GPT-4 等模型' },
    { value: 'claude', label: 'Claude', description: 'Anthropic Claude 模型' },
    { value: 'deepseek', label: 'DeepSeek', description: 'DeepSeek 深度求索模型' },
    { value: 'kimi', label: 'Kimi', description: 'Moonshot AI Kimi 模型' },
    { value: 'openrouter', label: 'OpenRouter', description: 'OpenRouter 路由服务' },
    { value: 'custom', label: 'Custom', description: '自定义 OpenAI 兼容端点' }
  ]
}

/**
 * 获取推荐的模型配置
 */
export function getRecommendedModels(provider: string): Array<{ value: string; label: string; description?: string }> {
  switch (provider) {
    case 'openai':
      return [
        { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', description: '快速且经济' },
        { value: 'gpt-4', label: 'GPT-4', description: '更强的理解能力' },
        { value: 'gpt-4-turbo', label: 'GPT-4 Turbo', description: '平衡性能与成本' }
      ]
    case 'claude':
      return [
        { value: 'claude-3-haiku-20240307', label: 'Claude 3 Haiku', description: '快速响应' },
        { value: 'claude-3-sonnet-20240229', label: 'Claude 3 Sonnet', description: '平衡性能' },
        { value: 'claude-3-opus-20240229', label: 'Claude 3 Opus', description: '最强性能' }
      ]
    case 'deepseek':
      return [
        { value: 'deepseek-chat', label: 'DeepSeek Chat', description: '通用对话模型' },
        { value: 'deepseek-coder', label: 'DeepSeek Coder', description: '代码专用模型' }
      ]
    case 'kimi':
      return [
        { value: 'moonshot-v1-8k', label: 'Moonshot v1 8K', description: '8K 上下文' },
        { value: 'moonshot-v1-32k', label: 'Moonshot v1 32K', description: '32K 上下文' },
        { value: 'moonshot-v1-128k', label: 'Moonshot v1 128K', description: '128K 上下文' }
      ]
    case 'openrouter':
      return [
        { value: 'openai/gpt-3.5-turbo', label: 'GPT-3.5 Turbo (via OpenRouter)' },
        { value: 'openai/gpt-4', label: 'GPT-4 (via OpenRouter)' },
        { value: 'anthropic/claude-3-sonnet', label: 'Claude 3 Sonnet (via OpenRouter)' }
      ]
    default:
      return [
        { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', description: '通用推荐' }
      ]
  }
}
