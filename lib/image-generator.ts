import { saveAs } from 'file-saver'

export interface ClipData {
  title: string
  content: string
  author?: string
  url: string
  site: string
  timestamp: string
  coverImage?: string
  tags?: string[]
}

export interface ImageGenerationOptions {
  width?: number
  height?: number
  scale?: number
  backgroundColor?: string
  quality?: number
  format?: 'png' | 'jpeg'
}

const defaultOptions: ImageGenerationOptions = {
  width: 800,
  scale: 2,
  backgroundColor: '#ffffff',
  quality: 0.92,
  format: 'png'
}

/**
 * 生成剪藏内容的图片
 * @param element 要转换的DOM元素
 * @param options 图片生成选项
 * @returns Promise<Blob> 生成的图片Blob
 */
export async function generateClipImage(
  element: HTMLElement,
  options: ImageGenerationOptions = {}
): Promise<Blob> {
  const opts = { ...defaultOptions, ...options }
  
  try {
    const html2canvas = (await import('html2canvas')).default
    const canvas = await html2canvas(element, {
      width: opts.width,
      height: opts.height,
      scale: opts.scale,
      backgroundColor: opts.backgroundColor,
      useCORS: true,
      allowTaint: true,
      scrollX: 0,
      scrollY: 0,
      windowWidth: opts.width,
      windowHeight: opts.height,
      onclone: (clonedDoc) => {
        // 确保克隆的文档样式正确
        const clonedElement = clonedDoc.querySelector('[data-clip-card]') as HTMLElement
        if (clonedElement) {
          clonedElement.style.transform = 'none'
          clonedElement.style.width = `${opts.width}px`
          clonedElement.style.minHeight = 'auto'
        }
      }
    })

    return new Promise((resolve, reject) => {
      canvas.toBlob(
        (blob) => {
          if (blob) {
            resolve(blob)
          } else {
            reject(new Error('Failed to generate image blob'))
          }
        },
        opts.format === 'jpeg' ? 'image/jpeg' : 'image/png',
        opts.quality
      )
    })
  } catch (error) {
    console.error('Error generating clip image:', error)
    throw new Error('Failed to generate clip image')
  }
}

/**
 * 将图片复制到剪贴板
 * @param blob 图片Blob
 */
export async function copyImageToClipboard(blob: Blob): Promise<void> {
  try {
    // 检查是否支持剪贴板API
    if (!navigator.clipboard || !navigator.clipboard.write) {
      throw new Error('Clipboard API not supported')
    }

    const clipboardItem = new ClipboardItem({
      [blob.type]: blob
    })

    await navigator.clipboard.write([clipboardItem])
  } catch (error) {
    console.error('Error copying image to clipboard:', error)
    throw new Error('Failed to copy image to clipboard')
  }
}

/**
 * 下载图片到本地
 * @param blob 图片Blob
 * @param filename 文件名（不含扩展名）
 * @param format 图片格式
 */
export function downloadImage(
  blob: Blob, 
  filename: string, 
  format: 'png' | 'jpeg' = 'png'
): void {
  try {
    const sanitizedFilename = filename
      .replace(/[<>:"/\\|?*]/g, '_')
      .replace(/\s+/g, '_')
      .substring(0, 100)
    
    const extension = format === 'jpeg' ? 'jpg' : format
    const fullFilename = `${sanitizedFilename}.${extension}`
    
    saveAs(blob, fullFilename)
  } catch (error) {
    console.error('Error downloading image:', error)
    throw new Error('Failed to download image')
  }
}

/**
 * 生成并复制剪藏图片到剪贴板
 * @param element 要转换的DOM元素
 * @param options 图片生成选项
 */
export async function generateAndCopyClipImage(
  element: HTMLElement,
  options: ImageGenerationOptions = {}
): Promise<void> {
  const blob = await generateClipImage(element, options)
  await copyImageToClipboard(blob)
}

/**
 * 生成并下载剪藏图片
 * @param element 要转换的DOM元素
 * @param filename 文件名
 * @param options 图片生成选项
 */
export async function generateAndDownloadClipImage(
  element: HTMLElement,
  filename: string,
  options: ImageGenerationOptions = {}
): Promise<void> {
  const blob = await generateClipImage(element, options)
  downloadImage(blob, filename, options.format)
}

/**
 * 预处理内容，确保图片正确显示
 * @param content 原始内容
 * @returns 处理后的内容
 */
export function preprocessContentForImage(content: string): string {
  // 移除可能影响图片生成的特殊字符和标签
  return content
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '') // 移除script标签
    .replace(/<style[^>]*>[\s\S]*?<\/style>/gi, '') // 移除style标签
    .replace(/data:image\/[^;]+;base64,/g, '') // 移除base64图片（可能太大）
    .trim()
}

/**
 * 创建用于图片生成的临时DOM元素
 * @param clipData 剪藏数据
 * @returns 临时DOM元素
 */
export function createTempElement(clipData: ClipData): HTMLElement {
  const tempDiv = document.createElement('div')
  tempDiv.style.position = 'fixed'
  tempDiv.style.left = '-9999px'
  tempDiv.style.top = '-9999px'
  tempDiv.style.width = '800px'
  tempDiv.style.zIndex = '-1000'
  tempDiv.setAttribute('data-clip-card', 'true')
  
  return tempDiv
}