import React from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { CalendarDays, ExternalLink, User } from 'lucide-react'

interface ClipCardProps {
  title: string
  content: string
  author?: string
  url: string
  site: string
  timestamp: string
  coverImage?: string
  tags?: string[]
  className?: string
}

const ClipCard: React.FC<ClipCardProps> = ({
  title,
  content,
  author,
  url,
  site,
  timestamp,
  coverImage,
  tags,
  className = ''
}) => {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getDomainFromUrl = (url: string) => {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return site
    }
  }

  return (
    <Card className={`w-full max-w-2xl mx-auto shadow-lg border-0 bg-white ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between gap-4">
          <div className="flex-1">
            <h1 className="text-xl font-bold text-gray-900 leading-tight mb-2 overflow-hidden">
              <div className="line-clamp-2">
                {title}
              </div>
            </h1>
            <div className="flex items-center gap-3 text-sm text-gray-600">
              <div className="flex items-center gap-1">
                <ExternalLink className="h-3 w-3" />
                <span className="truncate">{getDomainFromUrl(url)}</span>
              </div>
              <div className="flex items-center gap-1">
                <CalendarDays className="h-3 w-3" />
                <span>{formatDate(timestamp)}</span>
              </div>
            </div>
          </div>
          {coverImage && (
            <div className="flex-shrink-0">
              <img
                src={coverImage}
                alt="Cover"
                className="w-20 h-20 object-cover rounded-lg border border-gray-200"
              />
            </div>
          )}
        </div>
        
        {author && (
          <div className="flex items-center gap-2 mt-3 pt-3 border-t border-gray-100">
            <Avatar className="h-6 w-6">
              <AvatarImage src="" alt={author} />
              <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                {getInitials(author)}
              </AvatarFallback>
            </Avatar>
            <div className="flex items-center gap-1 text-sm text-gray-600">
              <User className="h-3 w-3" />
              <span>{author}</span>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="pt-0">
        <div className="prose prose-sm max-w-none text-gray-700 leading-relaxed">
          <div 
            className="break-words"
            dangerouslySetInnerHTML={{ 
              __html: content
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>')
            }}
          />
        </div>
        
        {tags && tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-4 pt-4 border-t border-gray-100">
            {tags.map((tag, index) => (
              <Badge
                key={index}
                variant="secondary"
                className="text-xs bg-gray-100 text-gray-600 hover:bg-gray-200"
              >
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
      
      <div className="px-6 pb-4">
        <div className="text-xs text-gray-400 text-center">
          Generated by mpclipper
        </div>
      </div>
    </Card>
  )
}

export default ClipCard