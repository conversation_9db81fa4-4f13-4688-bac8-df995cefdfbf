import React from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  MousePointer, 
  Loader2,
  Info
} from 'lucide-react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import GuidedButton from './GuidedButton'

interface ExtractionModeSelectorProps {
  // 操作回调
  onQuickExtract: () => void
  onAIExtract: () => void
  onSelectionMode: () => void
  
  // 状态
  extracting: boolean
  aiExtracting: boolean
  
  // 配置状态
  aiEnabled: boolean
  
  // 引导
  onGoToSettings: () => void
}

const ExtractionModeSelector: React.FC<ExtractionModeSelectorProps> = ({
  onQuickExtract,
  onAIExtract,
  onSelectionMode,
  extracting,
  aiExtracting,
  aiEnabled,
  onGoToSettings
}) => {
  const anyExtracting = extracting || aiExtracting
  
  return (
    <TooltipProvider>
      <Card className="border-b rounded-none">
        <CardHeader className="pb-3">
          <div className="flex items-center gap-2">
            <h3 className="text-sm font-medium text-foreground">采集方式</h3>
            <Tooltip>
              <TooltipTrigger>
                <Info className="h-3 w-3 text-muted-foreground" />
              </TooltipTrigger>
              <TooltipContent side="right" className="max-w-xs">
                <div className="space-y-1 text-xs">
                  <p><strong>快速剪藏：</strong>使用传统算法提取内容，速度快</p>
                  <p><strong>AI剪藏：</strong>使用AI智能分析页面结构，质量更高</p>
                  <p><strong>交互选择：</strong>手动选择页面元素，精确控制</p>
                </div>
              </TooltipContent>
            </Tooltip>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          <div className="grid grid-cols-3 gap-2">
            {/* 快速剪藏 */}
            <Button
              onClick={onQuickExtract}
              disabled={anyExtracting}
              variant={extracting ? "default" : "outline"}
              size="sm"
              className="flex-col h-auto py-3 px-2"
            >
              {extracting ? (
                <Loader2 className="h-4 w-4 mb-1 animate-spin" />
              ) : (
                <Zap className="h-4 w-4 mb-1" />
              )}
              <span className="text-xs">快速剪藏</span>
              <Badge variant="secondary" className="text-xs mt-1 px-1">
                传统算法
              </Badge>
            </Button>
            
            {/* AI剪藏 */}
            <GuidedButton
              enabled={aiEnabled}
              serviceType="ai"
              requiredItems={['AI服务商', 'API Key']}
              onGoToSettings={onGoToSettings}
              onClick={onAIExtract}
              disabled={anyExtracting}
              variant={aiExtracting ? "default" : "outline"}
              size="sm"
              className="flex-col h-auto py-3 px-2"
            >
              {aiExtracting ? (
                <Loader2 className="h-4 w-4 mb-1 animate-spin" />
              ) : (
                <Bot className="h-4 w-4 mb-1" />
              )}
              <span className="text-xs">AI剪藏</span>
              <Badge variant="secondary" className="text-xs mt-1 px-1">
                智能分析
              </Badge>
            </GuidedButton>
            
            {/* 交互选择 */}
            <Button
              onClick={onSelectionMode}
              disabled={anyExtracting}
              variant="outline"
              size="sm"
              className="flex-col h-auto py-3 px-2"
            >
              <MousePointer className="h-4 w-4 mb-1" />
              <span className="text-xs">交互选择</span>
              <Badge variant="secondary" className="text-xs mt-1 px-1">
                手动选择
              </Badge>
            </Button>
          </div>
          
          {/* 状态说明 */}
          {anyExtracting && (
            <div className="mt-3 p-2 bg-muted rounded-md">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Loader2 className="h-3 w-3 animate-spin" />
                <span>
                  {extracting && "正在使用传统算法提取页面内容..."}
                  {aiExtracting && "正在使用AI分析页面结构，请稍候..."}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </TooltipProvider>
  )
}

export default ExtractionModeSelector
