import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { 
  ChevronDown,
  ChevronRight,
  Edit3,
  Eye
} from 'lucide-react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'

interface ContentEditorProps {
  content: string
  onContentChange: (content: string) => void
  defaultExpanded?: boolean
}

const ContentEditor: React.FC<ContentEditorProps> = ({
  content,
  onContentChange,
  defaultExpanded = false
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)
  const [isEditing, setIsEditing] = useState(false)
  const [tempContent, setTempContent] = useState(content)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    setTempContent(content)
  }, [content])

  // 自动调整textarea高度
  useEffect(() => {
    if (textareaRef.current && isEditing) {
      const textarea = textareaRef.current
      textarea.style.height = 'auto'
      textarea.style.height = Math.max(200, textarea.scrollHeight) + 'px'
    }
  }, [tempContent, isEditing])

  const handleSave = () => {
    onContentChange(tempContent)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setTempContent(content)
    setIsEditing(false)
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
    if (!isExpanded) {
      setIsEditing(false)
    }
  }

  const handleEdit = (e: React.MouseEvent) => {
    e.stopPropagation()
    if (!isExpanded) {
      setIsExpanded(true)
    }
    setIsEditing(true)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      handleCancel()
    }
    // Ctrl/Cmd + Enter 保存
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      handleSave()
    }
  }

  return (
    <div className="border-b">
      {/* 头部 */}
      <div 
        className="flex items-center justify-between px-5 py-3 cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={toggleExpanded}
      >
        <div className="flex items-center gap-2">
          <span className="text-base font-medium text-foreground">正文</span>
          {!isEditing && (
            <Button
              onClick={handleEdit}
              variant="ghost"
              size="icon"
              className="h-6 w-6"
            >
              <Edit3 className="h-3 w-3" />
            </Button>
          )}
        </div>
        <Button variant="ghost" size="icon" className="h-6 w-6">
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* 内容 */}
      {isExpanded && (
        <div className="px-5 pb-4">
          {isEditing ? (
            <div className="space-y-3">
              <Textarea
                ref={textareaRef}
                value={tempContent}
                onChange={(e) => setTempContent(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="输入markdown格式的正文内容..."
                className="min-h-[200px] font-mono text-sm resize-none border-muted"
                autoFocus
              />
              <div className="flex items-center justify-between">
                <div className="text-xs text-muted-foreground">
                  提示：Ctrl/Cmd + Enter 保存，Esc 取消
                </div>
                <div className="flex gap-2">
                  <Button onClick={handleCancel} variant="outline" size="sm">
                    取消
                  </Button>
                  <Button onClick={handleSave} size="sm">
                    保存
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            <div 
              className="prose prose-sm max-w-none dark:prose-invert cursor-text min-h-[100px] p-3 rounded border hover:border-primary/50 transition-colors break-words"
              onClick={handleEdit}
            >
              {content ? (
                <ReactMarkdown
                  remarkPlugins={[remarkGfm]}
                  rehypePlugins={[rehypeRaw]}
                  components={{
                    // 自定义图片渲染
                    img: ({ node, ...props }) => (
                      <img
                        {...props}
                        className="max-w-full h-auto rounded border"
                        loading="lazy"
                      />
                    ),
                    // 自定义链接渲染
                    a: ({ node, ...props }) => (
                      <a
                        {...props}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline break-all"
                      />
                    ),
                    // 自定义代码块渲染
                    pre: ({ node, ...props }) => (
                      <pre
                        {...props}
                        className="bg-muted p-3 rounded text-sm overflow-x-auto"
                      />
                    ),
                    // 自定义表格渲染
                    table: ({ node, ...props }) => (
                      <div className="overflow-x-auto">
                        <table {...props} className="w-full border-collapse border border-border" />
                      </div>
                    ),
                    th: ({ node, ...props }) => (
                      <th {...props} className="border border-border px-3 py-2 bg-muted font-medium text-left" />
                    ),
                    td: ({ node, ...props }) => (
                      <td {...props} className="border border-border px-3 py-2" />
                    )
                  }}
                >
                  {content}
                </ReactMarkdown>
              ) : (
                <div className="text-muted-foreground text-sm italic py-8 text-center">
                  点击此处编辑正文内容...
                </div>
              )}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default ContentEditor
