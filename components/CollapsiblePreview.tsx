import React, { useState } from 'react'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { CalendarDays, ExternalLink, User, ChevronDown, ChevronUp } from 'lucide-react'

interface CollapsiblePreviewProps {
  title: string
  content: string
  author?: string
  url: string
  site: string
  timestamp: string
  coverImage?: string
  tags?: string[]
  className?: string
  defaultExpanded?: boolean
}

const CollapsiblePreview: React.FC<CollapsiblePreviewProps> = ({
  title,
  content,
  author,
  url,
  site,
  timestamp,
  coverImage,
  tags,
  className = '',
  defaultExpanded = false
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded)

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getDomainFromUrl = (url: string) => {
    try {
      return new URL(url).hostname.replace('www.', '')
    } catch {
      return site
    }
  }

  const truncateContent = (text: string, maxLength: number = 120) => {
    if (text.length <= maxLength) return text
    return text.substring(0, maxLength) + '...'
  }

  return (
    <Card className={`w-full shadow-sm border ${className}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <h3 className="text-sm font-medium text-foreground">内容预览</h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        {/* 折叠时的简要预览 */}
        <div className="space-y-2">
          <div className="flex items-start gap-3">
            {coverImage && (
              <img
                src={coverImage}
                alt="Cover"
                className="w-12 h-12 object-cover rounded border border-border flex-shrink-0"
              />
            )}
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-medium text-foreground line-clamp-2 leading-relaxed">
                {title}
              </h4>
              <div className="flex items-center gap-2 text-xs text-muted-foreground mt-1">
                <span className="truncate">{getDomainFromUrl(url)}</span>
                <span>•</span>
                <span>{formatDate(timestamp)}</span>
              </div>
            </div>
          </div>
          
          {!isExpanded && (
            <p className="text-xs text-muted-foreground line-clamp-2 leading-relaxed">
              {truncateContent(content.replace(/[#*\[\]()]/g, '').replace(/\n/g, ' '))}
            </p>
          )}
        </div>
      </CardHeader>

      {/* 展开时的完整预览 */}
      {isExpanded && (
        <CardContent className="pt-0">
          <div className="space-y-4">
            {/* 详细信息 */}
            <div className="space-y-3">
              <div className="flex items-center gap-3 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <ExternalLink className="h-3 w-3" />
                  <span className="truncate">{getDomainFromUrl(url)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <CalendarDays className="h-3 w-3" />
                  <span>{formatDate(timestamp)}</span>
                </div>
              </div>
              
              {author && (
                <div className="flex items-center gap-2">
                  <Avatar className="h-5 w-5">
                    <AvatarImage src="" alt={author} />
                    <AvatarFallback className="text-xs bg-muted text-muted-foreground">
                      {getInitials(author)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex items-center gap-1 text-sm text-muted-foreground">
                    <User className="h-3 w-3" />
                    <span>{author}</span>
                  </div>
                </div>
              )}
            </div>

            {/* 内容正文 */}
            <div className="prose prose-sm max-w-none text-foreground leading-relaxed">
              <div 
                className="break-words text-sm"
                dangerouslySetInnerHTML={{ 
                  __html: content
                    .replace(/\n/g, '<br>')
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                }}
              />
            </div>
            
            {/* 标签 */}
            {tags && tags.length > 0 && (
              <div className="flex flex-wrap gap-1 pt-3 border-t border-border">
                {tags.map((tag, index) => (
                  <Badge
                    key={index}
                    variant="secondary"
                    className="text-xs"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      )}
    </Card>
  )
}

export default CollapsiblePreview
