import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { 
  Download,
  Target,
  Loader2,
  AlertCircle
} from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ExportSectionProps {
  onLocalSave: () => void
  onSyncNotion: () => void
  notionEnabled: boolean
  syncing: boolean
  onGoToSettings: () => void
  hasContent: boolean
}

const ExportSection: React.FC<ExportSectionProps> = ({
  onLocalSave,
  onSyncNotion,
  notionEnabled,
  syncing,
  onGoToSettings,
  hasContent
}) => {
  return (
    <div className="p-5 bg-muted/30">
      <div className="space-y-3">
        <h3 className="text-sm font-medium text-muted-foreground">导出到</h3>
        
        <div className="grid grid-cols-2 gap-3">
          {/* Obsidian */}
          <Button
            onClick={onLocalSave}
            disabled={!hasContent}
            variant="outline"
            className="flex-col h-auto py-3 gap-2"
          >
            <Target className="h-5 w-5" />
            <span className="text-sm">Obsidian</span>
          </Button>

          {/* Notion */}
          <Button
            onClick={notionEnabled ? onSyncNotion : onGoToSettings}
            disabled={!hasContent || syncing}
            variant={notionEnabled ? "default" : "outline"}
            className="flex-col h-auto py-3 gap-2"
          >
            {syncing ? (
              <Loader2 className="h-5 w-5 animate-spin" />
            ) : (
              <Download className="h-5 w-5" />
            )}
            <span className="text-sm">
              {syncing ? '同步中...' : 'Notion'}
            </span>
          </Button>
        </div>

        {/* 配置提示 */}
        {!notionEnabled && (
          <Alert className="py-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Notion 服务未配置，
              <Button 
                variant="link" 
                className="h-auto p-0 text-xs underline" 
                onClick={onGoToSettings}
              >
                点击设置
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {!hasContent && (
          <Alert className="py-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              请先采集内容再进行导出
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}

export default ExportSection
