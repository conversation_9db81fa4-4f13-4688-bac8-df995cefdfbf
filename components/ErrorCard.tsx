import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  AlertCircle, 
  RefreshCw, 
  Settings, 
  HelpCircle,
  ExternalLink
} from 'lucide-react'

interface ErrorCardProps {
  title: string
  message: string
  suggestions?: string[]
  type?: 'warning' | 'error' | 'info'
  onRetry?: () => void
  onGoToSettings?: () => void
  onShowHelp?: () => void
  helpUrl?: string
  canRetry?: boolean
  canConfigure?: boolean
}

const ErrorCard: React.FC<ErrorCardProps> = ({
  title,
  message,
  suggestions = [],
  type = 'error',
  onRetry,
  onGoToSettings,
  onShowHelp,
  helpUrl,
  canRetry = true,
  canConfigure = true
}) => {
  const getVariant = () => {
    switch (type) {
      case 'warning': return 'border-amber-200 bg-amber-50'
      case 'info': return 'border-blue-200 bg-blue-50'
      default: return 'border-red-200 bg-red-50'
    }
  }

  const getIconColor = () => {
    switch (type) {
      case 'warning': return 'text-amber-600'
      case 'info': return 'text-blue-600'
      default: return 'text-red-600'
    }
  }

  const getBadgeVariant = () => {
    switch (type) {
      case 'warning': return 'bg-amber-100 text-amber-800 border-amber-300'
      case 'info': return 'bg-blue-100 text-blue-800 border-blue-300'
      default: return 'bg-red-100 text-red-800 border-red-300'
    }
  }

  return (
    <Card className={`${getVariant()} border-l-4`}>
      <CardHeader className="pb-3">
        <div className="flex items-start gap-3">
          <AlertCircle className={`h-5 w-5 ${getIconColor()} flex-shrink-0 mt-0.5`} />
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <CardTitle className="text-base">{title}</CardTitle>
              <Badge variant="outline" className={`text-xs ${getBadgeVariant()}`}>
                {type === 'warning' ? '警告' : type === 'info' ? '提示' : '错误'}
              </Badge>
            </div>
            <CardDescription className="text-sm">
              {message}
            </CardDescription>
          </div>
        </div>
      </CardHeader>
      
      {suggestions.length > 0 && (
        <CardContent className="pt-0 pb-3">
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">建议解决方案：</h4>
            <ul className="space-y-1">
              {suggestions.map((suggestion, index) => (
                <li key={index} className="text-xs text-muted-foreground flex items-start gap-2">
                  <div className="w-1 h-1 rounded-full bg-current mt-2 flex-shrink-0" />
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>
        </CardContent>
      )}
      
      <CardContent className="pt-0">
        <div className="flex gap-2 flex-wrap">
          {canRetry && onRetry && (
            <Button onClick={onRetry} size="sm" variant="outline" className="flex-1 min-w-20">
              <RefreshCw className="h-3 w-3 mr-1" />
              重试
            </Button>
          )}
          
          {canConfigure && onGoToSettings && (
            <Button onClick={onGoToSettings} size="sm" variant="outline" className="flex-1 min-w-20">
              <Settings className="h-3 w-3 mr-1" />
              去配置
            </Button>
          )}
          
          {onShowHelp && (
            <Button onClick={onShowHelp} size="sm" variant="ghost">
              <HelpCircle className="h-3 w-3 mr-1" />
              帮助
            </Button>
          )}
          
          {helpUrl && (
            <Button asChild size="sm" variant="ghost">
              <a href={helpUrl} target="_blank" rel="noopener noreferrer">
                <ExternalLink className="h-3 w-3 mr-1" />
                查看文档
              </a>
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default ErrorCard
