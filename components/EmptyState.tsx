import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Zap, 
  Bot, 
  MousePointer, 
  Settings,
  CheckCircle,
  AlertCircle
} from 'lucide-react'

interface EmptyStateProps {
  onQuickExtract: () => void
  onAIExtract: () => void
  onSelectionMode: () => void
  onGoToSettings: () => void
  aiEnabled: boolean
  imageEnabled: boolean
  notionEnabled: boolean
  extracting: boolean
  aiExtracting: boolean
}

const EmptyState: React.FC<EmptyStateProps> = ({
  onQuickExtract,
  onAIExtract,
  onSelectionMode,
  onGoToSettings,
  aiEnabled,
  imageEnabled,
  notionEnabled,
  extracting,
  aiExtracting
}) => {
  return (
    <div className="space-y-6 p-4">
      {/* 主标题 */}
      <div className="text-center space-y-2">
        <h2 className="text-xl font-semibold text-foreground">开始剪藏网页内容</h2>
        <p className="text-sm text-muted-foreground">
          选择一种方式提取当前页面的精彩内容
        </p>
      </div>

      {/* 主操作卡片 */}
      <div className="grid gap-4">
        {/* 快捷剪藏 */}
        <Card className="cursor-pointer transition-all hover:shadow-md">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-primary/10">
                <Zap className="h-5 w-5 text-primary" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-base">快捷剪藏</CardTitle>
                <CardDescription className="text-xs">
                  智能识别正文内容，快速提取
                </CardDescription>
              </div>
              <Badge variant="outline" className="text-xs">推荐</Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <Button 
              onClick={onQuickExtract}
              disabled={extracting || aiExtracting}
              className="w-full"
              size="sm"
            >
              {extracting ? "提取中..." : "开始剪藏"}
            </Button>
          </CardContent>
        </Card>

        {/* AI辅助剪藏 */}
        <Card className="cursor-pointer transition-all hover:shadow-md">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-blue-500/10">
                <Bot className="h-5 w-5 text-blue-600" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-base">AI辅助剪藏</CardTitle>
                <CardDescription className="text-xs">
                  使用AI分析页面结构，提升提取精度
                </CardDescription>
              </div>
              {aiEnabled ? (
                <Badge variant="default" className="text-xs bg-blue-600">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  已配置
                </Badge>
              ) : (
                <Badge variant="secondary" className="text-xs">
                  <AlertCircle className="h-3 w-3 mr-1" />
                  未配置
                </Badge>
              )}
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <Button 
              onClick={aiEnabled ? onAIExtract : onGoToSettings}
              disabled={extracting || aiExtracting}
              variant={aiEnabled ? "default" : "outline"}
              className="w-full"
              size="sm"
            >
              {aiExtracting ? "AI分析中..." : aiEnabled ? "AI剪藏" : "去配置AI"}
            </Button>
          </CardContent>
        </Card>

        {/* 交互式选择 */}
        <Card className="cursor-pointer transition-all hover:shadow-md">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-green-500/10">
                <MousePointer className="h-5 w-5 text-green-600" />
              </div>
              <div className="flex-1">
                <CardTitle className="text-base">交互式选择</CardTitle>
                <CardDescription className="text-xs">
                  在页面上框选特定区域进行精确剪藏
                </CardDescription>
              </div>
              <Badge variant="outline" className="text-xs">精确</Badge>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <Button 
              onClick={onSelectionMode}
              disabled={extracting || aiExtracting}
              variant="outline"
              className="w-full"
              size="sm"
            >
              开始选择
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 能力状态概览 */}
      <div className="mt-6 p-4 bg-muted/30 rounded-lg">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium">服务状态</h3>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={onGoToSettings}
            className="h-auto p-1 text-xs"
          >
            <Settings className="h-3 w-3 mr-1" />
            配置
          </Button>
        </div>
        <div className="grid grid-cols-3 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${aiEnabled ? 'bg-green-500' : 'bg-gray-300'}`} />
            <span className="text-muted-foreground">AI助手</span>
          </div>
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${imageEnabled ? 'bg-green-500' : 'bg-gray-300'}`} />
            <span className="text-muted-foreground">图床</span>
          </div>
          <div className="flex items-center gap-1">
            <div className={`w-2 h-2 rounded-full ${notionEnabled ? 'bg-green-500' : 'bg-gray-300'}`} />
            <span className="text-muted-foreground">Notion</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EmptyState
