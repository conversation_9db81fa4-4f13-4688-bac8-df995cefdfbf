import React, { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  AlertCircle, 
  Loader2,
  Upload,
  Cloud,
  ArrowRight,
  Settings
} from 'lucide-react'

interface WorkflowStep {
  id: string
  title: string
  description: string
  status: 'pending' | 'running' | 'completed' | 'error' | 'skipped'
  errorMessage?: string
}

interface OneClickWorkflowProps {
  onUploadImages: () => Promise<void>
  onSyncNotion: () => Promise<void>
  onGoToSettings: () => void
  imageEnabled: boolean
  notionEnabled: boolean
  hasImages: boolean
  className?: string
}

const OneClickWorkflow: React.FC<OneClickWorkflowProps> = ({
  onUploadImages,
  onSyncNotion,
  onGoToSettings,
  imageEnabled,
  notionEnabled,
  hasImages,
  className
}) => {
  const [isRunning, setIsRunning] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)
  const [steps, setSteps] = useState<WorkflowStep[]>([
    {
      id: 'upload',
      title: '上传图片',
      description: '将图片上传到云存储并替换链接',
      status: 'pending'
    },
    {
      id: 'sync',
      title: '同步Notion',
      description: '将内容同步到Notion数据库',
      status: 'pending'
    }
  ])

  const updateStepStatus = (stepId: string, status: WorkflowStep['status'], errorMessage?: string) => {
    setSteps(prev => prev.map(step => 
      step.id === stepId 
        ? { ...step, status, errorMessage } 
        : step
    ))
  }

  const getStepIcon = (step: WorkflowStep) => {
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'running':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-600" />
      case 'skipped':
        return <div className="h-4 w-4 rounded-full bg-gray-300" />
      default:
        return <div className="h-4 w-4 rounded-full bg-gray-200" />
    }
  }

  const getStepBadge = (step: WorkflowStep) => {
    const badgeMap = {
      pending: { text: '待执行', variant: 'secondary' as const },
      running: { text: '执行中', variant: 'default' as const },
      completed: { text: '已完成', variant: 'default' as const },
      error: { text: '失败', variant: 'destructive' as const },
      skipped: { text: '已跳过', variant: 'outline' as const }
    }
    
    const config = badgeMap[step.status]
    return <Badge variant={config.variant} className="text-xs">{config.text}</Badge>
  }

  const checkPrerequisites = () => {
    const issues: string[] = []
    
    if (hasImages && !imageEnabled) {
      issues.push('需要配置图床服务以上传图片')
    }
    
    if (!notionEnabled) {
      issues.push('需要配置Notion服务以同步内容')
    }
    
    return issues
  }

  const runWorkflow = async () => {
    const issues = checkPrerequisites()
    if (issues.length > 0) {
      // 显示配置提示
      return
    }

    setIsRunning(true)
    setCurrentStep(0)

    try {
      // 步骤1: 上传图片
      if (hasImages && imageEnabled) {
        setCurrentStep(0)
        updateStepStatus('upload', 'running')
        
        try {
          await onUploadImages()
          updateStepStatus('upload', 'completed')
        } catch (error) {
          updateStepStatus('upload', 'error', error instanceof Error ? error.message : '上传失败')
          throw error
        }
      } else {
        updateStepStatus('upload', 'skipped')
      }

      // 步骤2: 同步Notion
      if (notionEnabled) {
        setCurrentStep(1)
        updateStepStatus('sync', 'running')
        
        try {
          await onSyncNotion()
          updateStepStatus('sync', 'completed')
        } catch (error) {
          updateStepStatus('sync', 'error', error instanceof Error ? error.message : '同步失败')
          throw error
        }
      } else {
        updateStepStatus('sync', 'skipped')
      }

    } catch (error) {
      console.error('一键完成流程失败:', error)
    } finally {
      setIsRunning(false)
    }
  }

  const prerequisites = checkPrerequisites()
  const canRun = prerequisites.length === 0

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base flex items-center gap-2">
          <ArrowRight className="h-4 w-4" />
          一键完成
        </CardTitle>
        <CardDescription className="text-xs">
          自动上传图片并同步到Notion
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 前置检查 */}
        {prerequisites.length > 0 && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              <div className="space-y-1">
                <p className="font-medium">需要先完成配置：</p>
                <ul className="space-y-1 ml-4">
                  {prerequisites.map((issue, index) => (
                    <li key={index} className="text-xs flex items-start gap-1">
                      <span>•</span>
                      <span>{issue}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* 执行步骤 */}
        <div className="space-y-3">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center gap-3 p-2 rounded bg-muted/30">
              {getStepIcon(step)}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium">{step.title}</span>
                  {getStepBadge(step)}
                </div>
                <p className="text-xs text-muted-foreground">{step.description}</p>
                {step.errorMessage && (
                  <p className="text-xs text-red-600 mt-1">{step.errorMessage}</p>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* 进度条 */}
        {isRunning && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-xs">
              <span>执行进度</span>
              <span>{currentStep + 1}/{steps.length}</span>
            </div>
            <Progress value={((currentStep + 1) / steps.length) * 100} className="h-1" />
          </div>
        )}

        {/* 操作按钮 */}
        <div className="flex gap-2">
          <Button 
            onClick={runWorkflow}
            disabled={!canRun || isRunning}
            className="flex-1"
            size="sm"
          >
            {isRunning ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                执行中...
              </>
            ) : (
              '开始执行'
            )}
          </Button>
          
          {!canRun && (
            <Button 
              onClick={onGoToSettings}
              variant="outline"
              size="sm"
            >
              <Settings className="h-3 w-3 mr-1" />
              去配置
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default OneClickWorkflow
