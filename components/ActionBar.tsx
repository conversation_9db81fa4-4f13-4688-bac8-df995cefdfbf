import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { 
  <PERSON><PERSON>, 
  Bo<PERSON>, 
  MousePointer, 
  Edit, 
  Tags, 
  Image,
  Download,
  Upload,
  Cloud,
  Loader2
} from 'lucide-react'
import GuidedButton from './GuidedButton'

interface ActionBarProps {
  // 采集相关
  onQuickExtract: () => void
  onAIExtract: () => void
  onSelectionMode: () => void
  extracting: boolean
  aiExtracting: boolean
  aiEnabled: boolean
  
  // 编辑相关
  onEditTitle: () => void
  onEditTags: () => void
  onSelectCover: () => void
  hasContent: boolean
  
  // 输出相关
  onLocalSave: () => void
  onUploadImages: () => void
  onSyncNotion: () => void
  imageEnabled: boolean
  notionEnabled: boolean
  uploading: boolean
  syncing: boolean
  
  // 引导相关
  onGoToSettings: () => void
}

const ActionBar: React.FC<ActionBarProps> = ({
  onQuickExtract,
  onAIExtract,
  onSelectionMode,
  extracting,
  aiExtracting,
  aiEnabled,
  
  onEditTitle,
  onEditTags,
  onSelectCover,
  hasContent,
  
  onLocalSave,
  onUploadImages,
  onSyncNotion,
  imageEnabled,
  notionEnabled,
  uploading,
  syncing,
  
  onGoToSettings
}) => {
  const anyExtracting = extracting || aiExtracting
  
  return (
    <Card className="border-b rounded-none">
      <CardContent className="p-3">
        <div className="space-y-3">
          {/* 采集操作组 */}
          <div className="space-y-2">
            <h3 className="text-xs font-medium text-muted-foreground">采集内容</h3>
            <div className="flex gap-2">
              <Button
                onClick={onQuickExtract}
                disabled={anyExtracting}
                size="sm"
                className="flex-1"
                aria-label="快捷剪藏 (快捷键: E)"
                title="快捷键: E"
              >
                {extracting ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    提取中
                  </>
                ) : (
                  <>
                    <Zap className="h-3 w-3 mr-1" />
                    快捷剪藏
                  </>
                )}
              </Button>
              
              <GuidedButton
                enabled={aiEnabled}
                serviceType="ai"
                requiredItems={['AI服务商', 'API Key', '模型配置']}
                onGoToSettings={onGoToSettings}
                onClick={onAIExtract}
                disabled={anyExtracting}
                size="sm"
                className="flex-1"
                aria-label="AI辅助剪藏 (快捷键: A)"
                title="快捷键: A"
              >
                {aiExtracting ? (
                  <>
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    AI分析
                  </>
                ) : (
                  <>
                    <Bot className="h-3 w-3 mr-1" />
                    AI剪藏
                  </>
                )}
              </GuidedButton>
              
              <Button
                onClick={onSelectionMode}
                disabled={anyExtracting}
                variant="outline"
                size="sm"
                className="flex-1"
                aria-label="交互式选择 (快捷键: S)"
                title="快捷键: S"
              >
                <MousePointer className="h-3 w-3 mr-1" />
                选择
              </Button>
            </div>
          </div>

          {hasContent && (
            <>
              <Separator />
              
              {/* 编辑操作组 */}
              <div className="space-y-2">
                <h3 className="text-xs font-medium text-muted-foreground">编辑内容</h3>
                <div className="flex gap-2">
                  <Button
                    onClick={onEditTitle}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    标题
                  </Button>
                  <Button
                    onClick={onEditTags}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Tags className="h-3 w-3 mr-1" />
                    标签
                  </Button>
                  <Button
                    onClick={onSelectCover}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Image className="h-3 w-3 mr-1" />
                    封面
                  </Button>
                </div>
              </div>

              <Separator />
              
              {/* 输出操作组 */}
              <div className="space-y-2">
                <h3 className="text-xs font-medium text-muted-foreground">导出保存</h3>
                <div className="flex gap-2">
                  <Button
                    onClick={onLocalSave}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    <Download className="h-3 w-3 mr-1" />
                    本地
                  </Button>
                  
                  <GuidedButton
                    enabled={imageEnabled}
                    serviceType="image"
                    requiredItems={['图床服务商', 'Access Key', 'Secret Key', 'Bucket']}
                    onGoToSettings={onGoToSettings}
                    onClick={onUploadImages}
                    disabled={uploading || syncing}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    {uploading ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        上传中
                      </>
                    ) : (
                      <>
                        <Upload className="h-3 w-3 mr-1" />
                        图床
                      </>
                    )}
                  </GuidedButton>
                  
                  <GuidedButton
                    enabled={notionEnabled}
                    serviceType="notion"
                    requiredItems={['Integration Token', 'Database ID']}
                    onGoToSettings={onGoToSettings}
                    onClick={onSyncNotion}
                    disabled={uploading || syncing}
                    variant="outline"
                    size="sm"
                    className="flex-1"
                  >
                    {syncing ? (
                      <>
                        <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                        同步中
                      </>
                    ) : (
                      <>
                        <Cloud className="h-3 w-3 mr-1" />
                        Notion
                      </>
                    )}
                  </GuidedButton>
                </div>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default ActionBar
