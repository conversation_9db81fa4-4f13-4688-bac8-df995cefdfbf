import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { 
  Bot,
  Zap, 
  Image,
  MousePointer,
  Settings,
  Loader2
} from 'lucide-react'

interface TopToolbarProps {
  // 操作回调
  onQuickExtract: () => void
  onAIExtract: () => void
  onImageUpload: () => void
  onSelectionMode: () => void
  onGoToSettings: () => void
  
  // 状态
  extracting: boolean
  aiExtracting: boolean
  uploading: boolean
  inSelectionMode: boolean
  
  // 配置状态  
  aiEnabled: boolean
  imageEnabled: boolean
  
  // 页面模式
  pageMode?: 'current' | 'latest' | 'welcome'
  currentPageInfo?: {
    url: string | null
    canClip: boolean
    title: string | null
  }
}

const TopToolbar: React.FC<TopToolbarProps> = ({
  onQuickExtract,
  onAIExtract,
  onImageUpload,
  onSelectionMode,
  onGoToSettings,
  extracting,
  aiExtracting,
  uploading,
  inSelectionMode,
  aiEnabled,
  imageEnabled,
  pageMode,
  currentPageInfo
}) => {
  const anyProcessing = extracting || aiExtracting || uploading
  const canClipCurrentPage = currentPageInfo?.canClip ?? false
  
  // 页面状态显示
  const getPageStatusInfo = () => {
    if (pageMode === 'latest') {
      return { text: '历史剪藏', color: 'text-amber-500' }
    }
    if (currentPageInfo?.canClip) {
      return { text: '可剪藏', color: 'text-green-500' }
    }
    return { text: '不可剪藏', color: 'text-red-500' }
  }

  const statusInfo = getPageStatusInfo()

  return (
    <TooltipProvider>
      <div className="flex items-center justify-between p-4 bg-muted/50 border-b">
        <div className="flex items-center gap-3">
          {/* 页面状态指示器 */}
          {pageMode !== 'welcome' && (
            <div className="flex items-center gap-2 px-2 py-1 rounded-md bg-background border text-xs">
              <div className={`w-2 h-2 rounded-full ${statusInfo.color.replace('text-', 'bg-')}`} />
              <span className={statusInfo.color}>{statusInfo.text}</span>
            </div>
          )}
          {/* AI剪藏 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onAIExtract}
                disabled={anyProcessing || !aiEnabled || !canClipCurrentPage}
                variant={aiExtracting ? "default" : "outline"}
                size="icon"
                className={`h-8 w-8 transition-all ${aiExtracting ? 'bg-primary' : ''}`}
              >
                {aiExtracting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Bot className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>
                AI剪藏 
                {!aiEnabled && ' (未配置)'}
                {!canClipCurrentPage && ' (当前页面不支持)'}
                {pageMode === 'latest' && ' (重新分析当前页面)'}
              </p>
            </TooltipContent>
          </Tooltip>

          {/* 快捷剪藏 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onQuickExtract}
                disabled={anyProcessing || !canClipCurrentPage}
                variant={extracting ? "default" : "outline"}
                size="icon"
                className={`h-8 w-8 transition-all ${extracting ? 'bg-primary' : ''}`}
              >
                {extracting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Zap className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>
                快捷剪藏
                {!canClipCurrentPage && ' (当前页面不支持)'}
                {pageMode === 'latest' && ' (重新剪藏当前页面)'}
              </p>
            </TooltipContent>
          </Tooltip>

          {/* 图片转存 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onImageUpload}
                disabled={anyProcessing || !imageEnabled}
                variant={uploading ? "default" : "outline"}
                size="icon"
                className={`h-8 w-8 transition-all ${uploading ? 'bg-primary' : ''}`}
              >
                {uploading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Image className="h-4 w-4" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>图片转存 {!imageEnabled && '(未配置)'}</p>
            </TooltipContent>
          </Tooltip>

          {/* 手动选择 */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                onClick={onSelectionMode}
                disabled={anyProcessing || !canClipCurrentPage}
                variant={inSelectionMode ? "default" : "outline"}
                size="icon"
                className={`h-8 w-8 transition-all ${inSelectionMode ? 'bg-primary' : ''}`}
              >
                <MousePointer className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>
                手动选择
                {!canClipCurrentPage && ' (当前页面不支持)'}
                {pageMode === 'latest' && ' (重新选择当前页面)'}
              </p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* 设置按钮 */}
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              onClick={onGoToSettings}
              variant="outline"
              size="icon"
              className="h-8 w-8"
            >
              <Settings className="h-4 w-4" />
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>设置</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  )
}

export default TopToolbar
