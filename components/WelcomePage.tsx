import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Zap, 
  Bot, 
  Image,
  MousePointer,
  Settings,
  Heart,
  BookOpen
} from 'lucide-react'

interface WelcomePageProps {
  onGoToSettings: () => void
}

const WelcomePage: React.FC<WelcomePageProps> = ({
  onGoToSettings
}) => {
  return (
    <div className="h-full flex flex-col items-center justify-center p-6 bg-gradient-to-br from-background to-muted/20">
      <div className="max-w-md w-full space-y-6">
        {/* 欢迎图标和标题 */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-primary rounded-2xl flex items-center justify-center">
            <Heart className="h-8 w-8 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-foreground">欢迎使用剪藏助手</h1>
            <p className="text-muted-foreground mt-2">
              智能提取网页内容，一键保存精彩片段
            </p>
          </div>
        </div>

        {/* 功能介绍卡片 */}
        <Card className="border-dashed">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BookOpen className="h-5 w-5" />
              开始剪藏网页内容
            </CardTitle>
            <CardDescription>
              选择一种方式提取当前页面的精彩内容
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-3">
              {/* 快捷剪藏 */}
              <div className="flex flex-col items-center p-3 rounded-lg border bg-card/50 hover:bg-card transition-colors">
                <Zap className="h-5 w-5 text-primary mb-2" />
                <span className="text-sm font-medium">快捷剪藏</span>
                <span className="text-xs text-muted-foreground text-center">
                  智能识别正文内容
                </span>
              </div>

              {/* AI剪藏 */}
              <div className="flex flex-col items-center p-3 rounded-lg border bg-card/50 hover:bg-card transition-colors">
                <Bot className="h-5 w-5 text-blue-500 mb-2" />
                <span className="text-sm font-medium">AI剪藏</span>
                <span className="text-xs text-muted-foreground text-center">
                  AI分析页面结构
                </span>
              </div>

              {/* 图片转存 */}
              <div className="flex flex-col items-center p-3 rounded-lg border bg-card/50 hover:bg-card transition-colors">
                <Image className="h-5 w-5 text-green-500 mb-2" />
                <span className="text-sm font-medium">图片转存</span>
                <span className="text-xs text-muted-foreground text-center">
                  上传图片到云端
                </span>
              </div>

              {/* 手动选择 */}
              <div className="flex flex-col items-center p-3 rounded-lg border bg-card/50 hover:bg-card transition-colors">
                <MousePointer className="h-5 w-5 text-purple-500 mb-2" />
                <span className="text-sm font-medium">手动选择</span>
                <span className="text-xs text-muted-foreground text-center">
                  精确选择区域
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 提示信息 */}
        <div className="text-center space-y-3">
          <p className="text-sm text-muted-foreground">
            请先打开一个普通网页，然后开始剪藏
          </p>
          <Button
            onClick={onGoToSettings}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Settings className="h-4 w-4" />
            配置服务
          </Button>
        </div>

        {/* 支持的功能 */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground">
            支持导出到 Obsidian、Notion 等平台
          </p>
        </div>
      </div>
    </div>
  )
}

export default WelcomePage
