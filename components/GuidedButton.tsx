import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Settings,
  Bot,
  Cloud,
  Camera,
  CheckCircle,
  AlertTriangle
} from 'lucide-react'

interface GuidedButtonProps {
  children: React.ReactNode
  enabled: boolean
  serviceType: 'ai' | 'notion' | 'image'
  requiredItems: string[]
  onGoToSettings: () => void
  onClick?: () => void
  disabled?: boolean
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link"
  size?: "default" | "sm" | "lg" | "icon"
  className?: string
}

const GuidedButton: React.FC<GuidedButtonProps> = ({
  children,
  enabled,
  serviceType,
  requiredItems,
  onGoToSettings,
  onClick,
  disabled,
  variant = "default",
  size = "default",
  className
}) => {
  const [showGuide, setShowGuide] = useState(false)

  const serviceConfig = {
    ai: {
      icon: <Bot className="h-4 w-4" />,
      title: 'AI助手服务',
      description: '智能分析页面结构，提升内容提取精度'
    },
    notion: {
      icon: <Cloud className="h-4 w-4" />,
      title: 'Notion同步服务',
      description: '将剪藏内容自动同步到Notion数据库'
    },
    image: {
      icon: <Camera className="h-4 w-4" />,
      title: '图床服务',
      description: '自动上传图片到云存储并替换链接'
    }
  }

  const config = serviceConfig[serviceType]

  const handleClick = () => {
    if (enabled && onClick) {
      onClick()
    } else if (!enabled) {
      setShowGuide(true)
    }
  }

  if (enabled) {
    return (
      <Button
        onClick={onClick}
        disabled={disabled}
        variant={variant}
        size={size}
        className={className}
      >
        {children}
      </Button>
    )
  }

  return (
    <Popover open={showGuide} onOpenChange={setShowGuide}>
      <PopoverTrigger asChild>
        <Button
          onClick={handleClick}
          disabled={disabled}
          variant="outline"
          size={size}
          className={`relative ${className}`}
        >
          {children}
          <AlertTriangle className="h-3 w-3 ml-1 text-amber-500" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="start">
        <Card className="border-0 shadow-none">
          <CardHeader className="pb-3">
            <div className="flex items-center gap-2">
              {config.icon}
              <div>
                <CardTitle className="text-sm">{config.title}</CardTitle>
                <CardDescription className="text-xs">
                  {config.description}
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-amber-500" />
                <span className="text-sm font-medium">需要配置以下项目：</span>
              </div>
              <ul className="space-y-1 ml-6">
                {requiredItems.map((item, index) => (
                  <li key={index} className="text-xs text-muted-foreground flex items-center gap-2">
                    <div className="w-1 h-1 rounded-full bg-current" />
                    {item}
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button 
                onClick={() => {
                  setShowGuide(false)
                  onGoToSettings()
                }}
                size="sm"
                className="flex-1"
              >
                <Settings className="h-3 w-3 mr-1" />
                去配置
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowGuide(false)}
              >
                取消
              </Button>
            </div>
          </CardContent>
        </Card>
      </PopoverContent>
    </Popover>
  )
}

export default GuidedButton
