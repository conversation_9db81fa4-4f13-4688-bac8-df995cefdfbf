import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { 
  Loader2, 
  X, 
  CheckCircle, 
  AlertCircle,
  Cloud,
  Upload,
  Bot
} from 'lucide-react'

export interface TaskProgressProps {
  type: 'ai' | 'upload' | 'notion' | 'download'
  status: 'running' | 'success' | 'error' | 'cancelled'
  title: string
  description?: string
  progress?: number
  onCancel?: () => void
  onRetry?: () => void
  onDismiss?: () => void
  result?: {
    pageUrl?: string
    pageId?: string
    uploadedCount?: number
    totalCount?: number
  }
}

const TaskProgress: React.FC<TaskProgressProps> = ({
  type,
  status,
  title,
  description,
  progress,
  onCancel,
  onRetry,
  onDismiss,
  result
}) => {
  const getIcon = () => {
    if (status === 'success') return <CheckCircle className="h-4 w-4 text-green-600" />
    if (status === 'error') return <AlertCircle className="h-4 w-4 text-red-600" />
    
    const iconMap = {
      ai: <Bot className="h-4 w-4 text-blue-600" />,
      upload: <Upload className="h-4 w-4 text-orange-600" />,
      notion: <Cloud className="h-4 w-4 text-purple-600" />,
      download: <Loader2 className="h-4 w-4 text-gray-600" />
    }
    
    return status === 'running' ? 
      <Loader2 className="h-4 w-4 animate-spin" /> : 
      iconMap[type]
  }

  const getVariant = () => {
    switch (status) {
      case 'success': return 'bg-green-50 border-green-200'
      case 'error': return 'bg-red-50 border-red-200'
      case 'running': return 'bg-blue-50 border-blue-200'
      default: return 'bg-gray-50 border-gray-200'
    }
  }

  return (
    <Card className={`fixed bottom-4 left-4 right-4 z-50 ${getVariant()}`}>
      <CardContent className="p-4">
        <div className="flex items-center gap-3">
          {getIcon()}
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h4 className="text-sm font-medium truncate">{title}</h4>
              {status === 'running' && progress !== undefined && (
                <span className="text-xs text-muted-foreground">
                  {Math.round(progress)}%
                </span>
              )}
            </div>
            
            {description && (
              <p className="text-xs text-muted-foreground truncate">{description}</p>
            )}
            
            {status === 'running' && progress !== undefined && (
              <Progress value={progress} className="mt-2 h-1" />
            )}
            
            {/* 成功结果展示 */}
            {status === 'success' && result && (
              <div className="mt-2 space-y-1">
                {result.pageUrl && (
                  <a 
                    href={result.pageUrl} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-xs text-blue-600 hover:underline block"
                  >
                    在Notion中查看 →
                  </a>
                )}
                {result.uploadedCount !== undefined && result.totalCount !== undefined && (
                  <p className="text-xs text-green-700">
                    已上传 {result.uploadedCount}/{result.totalCount} 张图片
                  </p>
                )}
              </div>
            )}
          </div>
          
          {/* 操作按钮 */}
          <div className="flex items-center gap-1">
            {status === 'running' && onCancel && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onCancel}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            
            {status === 'error' && onRetry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                className="h-6 px-2 text-xs"
              >
                重试
              </Button>
            )}
            
            {(status === 'success' || status === 'error') && onDismiss && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onDismiss}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default TaskProgress
