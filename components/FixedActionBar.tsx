import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { 
  Save, 
  Eye, 
  Loader2, 
  Download, 
  Upload, 
  Cloud,
  Settings,
  CheckCircle
} from 'lucide-react'

export type SaveDestination = 'local' | 'image' | 'notion'

interface FixedActionBarProps {
  // 保存目标
  saveDestination: SaveDestination
  onSaveDestinationChange: (destination: SaveDestination) => void
  
  // 主要操作
  onSave: () => void
  onPreview: () => void
  
  // 状态
  saving: boolean
  hasContent: boolean
  
  // 服务状态
  imageEnabled: boolean
  notionEnabled: boolean
  
  // 配置引导
  onGoToSettings: () => void
  
  // 成功状态
  saveSuccess?: boolean
}

const FixedActionBar: React.FC<FixedActionBarProps> = ({
  saveDestination,
  onSaveDestinationChange,
  onSave,
  onPreview,
  saving,
  hasContent,
  imageEnabled,
  notionEnabled,
  onGoToSettings,
  saveSuccess
}) => {
  const getSaveButtonText = () => {
    if (saving) return '保存中...'
    if (saveSuccess) return '已保存'
    
    switch (saveDestination) {
      case 'local':
        return '下载到本地'
      case 'image':
        return '上传到图床'
      case 'notion':
        return '同步到 Notion'
      default:
        return '保存'
    }
  }

  const getSaveButtonIcon = () => {
    if (saving) return <Loader2 className="h-4 w-4 animate-spin" />
    if (saveSuccess) return <CheckCircle className="h-4 w-4" />
    
    switch (saveDestination) {
      case 'local':
        return <Download className="h-4 w-4" />
      case 'image':
        return <Upload className="h-4 w-4" />
      case 'notion':
        return <Cloud className="h-4 w-4" />
      default:
        return <Save className="h-4 w-4" />
    }
  }

  const getDestinationStatus = (dest: SaveDestination) => {
    switch (dest) {
      case 'local':
        return { enabled: true, label: '本地下载' }
      case 'image':
        return { 
          enabled: imageEnabled, 
          label: '图床上传',
          requiredSettings: ['图床服务商', 'Access Key', 'Secret Key']
        }
      case 'notion':
        return { 
          enabled: notionEnabled, 
          label: 'Notion 同步',
          requiredSettings: ['Integration Token', 'Database ID']
        }
      default:
        return { enabled: false, label: '未知' }
    }
  }

  const renderDestinationOption = (dest: SaveDestination) => {
    const status = getDestinationStatus(dest)
    
    return (
      <div className="flex items-center justify-between w-full">
        <span>{status.label}</span>
        {!status.enabled && (
          <Badge variant="outline" className="text-xs">
            需配置
          </Badge>
        )}
      </div>
    )
  }

  const handleSave = () => {
    const status = getDestinationStatus(saveDestination)
    if (!status.enabled) {
      onGoToSettings()
      return
    }
    onSave()
  }

  return (
    <Card className="border-t border-l-0 border-r-0 border-b-0 rounded-none bg-background/95 backdrop-blur-sm">
      <div className="p-3 space-y-3">
        {/* 保存目标选择 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-xs font-medium text-muted-foreground">
              保存到
            </label>
            {!getDestinationStatus(saveDestination).enabled && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onGoToSettings}
                className="h-6 px-2 text-xs"
              >
                <Settings className="h-3 w-3 mr-1" />
                配置
              </Button>
            )}
          </div>
          <Select 
            value={saveDestination} 
            onValueChange={onSaveDestinationChange}
            disabled={saving}
          >
            <SelectTrigger className="h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="local">
                {renderDestinationOption('local')}
              </SelectItem>
              <SelectItem value="image">
                {renderDestinationOption('image')}
              </SelectItem>
              <SelectItem value="notion">
                {renderDestinationOption('notion')}
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* 主要操作按钮 */}
        <div className="flex gap-2">
          <Button
            onClick={handleSave}
            disabled={!hasContent || saving}
            className="flex-1 h-9"
            size="sm"
          >
            {getSaveButtonIcon()}
            <span className="ml-2">{getSaveButtonText()}</span>
          </Button>
          
          <Button
            onClick={onPreview}
            disabled={!hasContent || saving}
            variant="outline"
            size="sm"
            className="h-9 px-3"
          >
            <Eye className="h-4 w-4" />
          </Button>
        </div>

        {/* 状态提示 */}
        {!getDestinationStatus(saveDestination).enabled && (
          <div className="text-xs text-muted-foreground text-center">
            需要先配置 {getDestinationStatus(saveDestination).requiredSettings?.join('、')} 等设置
          </div>
        )}
      </div>
    </Card>
  )
}

export default FixedActionBar
