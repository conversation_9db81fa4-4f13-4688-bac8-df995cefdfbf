import React, { useState, useEffect } from 'react'
import { Input } from '@/components/ui/input'

interface EditableTitleProps {
  title: string
  onTitleChange: (title: string) => void
  placeholder?: string
}

const EditableTitle: React.FC<EditableTitleProps> = ({
  title,
  onTitleChange,
  placeholder = "点击编辑标题..."
}) => {
  const [isEditing, setIsEditing] = useState(false)
  const [tempTitle, setTempTitle] = useState(title)

  useEffect(() => {
    setTempTitle(title)
  }, [title])

  const handleSave = () => {
    onTitleChange(tempTitle)
    setIsEditing(false)
  }

  const handleCancel = () => {
    setTempTitle(title)
    setIsEditing(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSave()
    } else if (e.key === 'Escape') {
      handleCancel()
    }
  }

  if (isEditing) {
    return (
      <div className="px-5 py-4 border-b w-full max-w-full min-w-0">
        <Input
          value={tempTitle}
          onChange={(e) => setTempTitle(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleSave}
          className="text-xl font-semibold border-none p-0 h-auto bg-transparent focus-visible:ring-0 resize-none break-words w-full max-w-full"
          placeholder={placeholder}
          autoFocus
        />
      </div>
    )
  }

  return (
    <div
      className="px-5 py-4 border-b cursor-pointer hover:bg-muted/50 transition-colors w-full max-w-full min-w-0"
      onClick={() => setIsEditing(true)}
    >
      <h1
        className="text-xl font-semibold leading-tight text-foreground min-h-[28px] break-words hyphens-auto w-full max-w-full overflow-hidden"
        style={{
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          maxWidth: '100%',
          width: '100%',
          hyphens: 'auto'
        }}
      >
        {title || placeholder}
      </h1>
    </div>
  )
}

export default EditableTitle
