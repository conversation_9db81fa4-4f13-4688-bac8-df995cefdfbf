import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { 
  ChevronDown,
  ChevronRight,
  Globe,
  User,
  Calendar,
  Tag,
  FolderOpen,
  BookOpen
} from 'lucide-react'

interface PropertiesSectionProps {
  title: string
  source: string
  author?: string
  publishDate?: string
  created: string
  tags: string[]
  category: string
  isRead: boolean
  onIsReadChange: (checked: boolean) => void
  onTagsChange: (tags: string[]) => void
}

const PropertiesSection: React.FC<PropertiesSectionProps> = ({
  title,
  source,
  author,
  publishDate,
  created,
  tags,
  category,
  isRead,
  onIsReadChange,
  onTagsChange
}) => {
  const [isExpanded, setIsExpanded] = useState(true)

  const formatUrl = (url: string) => {
    try {
      const urlObj = new URL(url)
      // 返回完整URL，让CSS处理换行
      return urlObj.href
    } catch {
      // 如果URL解析失败，返回原始URL
      return url
    }
  }

  const properties = [
    { 
      icon: BookOpen, 
      label: 'title', 
      value: title,
      type: 'text'
    },
    { 
      icon: Globe, 
      label: 'source', 
      value: formatUrl(source),
      type: 'link',
      fullValue: source
    },
    { 
      icon: User, 
      label: 'author', 
      value: author || '',
      type: 'text'
    },
    { 
      icon: Calendar, 
      label: 'published', 
      value: publishDate || '',
      type: 'text'
    },
    { 
      icon: Calendar, 
      label: 'created', 
      value: created,
      type: 'text'
    },
    { 
      icon: Tag, 
      label: 'tags', 
      value: tags,
      type: 'tags'
    }
  ]

  return (
    <div className="border-b">
      {/* 头部 */}
      <div 
        className="flex items-center justify-between px-5 py-3 cursor-pointer hover:bg-muted/50 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <span className="text-sm font-medium text-muted-foreground">属性</span>
        <Button variant="ghost" size="icon" className="h-6 w-6">
          {isExpanded ? (
            <ChevronDown className="h-4 w-4" />
          ) : (
            <ChevronRight className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* 内容 */}
      {isExpanded && (
        <div className="px-5 pb-4 space-y-3">
          {properties.map((prop, index) => (
            <div key={index} className="flex items-start gap-3 py-1 min-w-0">
              <prop.icon className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
              <span className="text-sm text-muted-foreground min-w-[60px] flex-shrink-0">
                {prop.label}
              </span>
              <div className="flex-1 w-full max-w-full min-w-0 overflow-hidden">
                {prop.type === 'tags' ? (
                  <div className="flex flex-wrap gap-1 w-full max-w-full">
                    {(prop.value as string[]).map((tag, tagIndex) => (
                      <Badge
                        key={tagIndex}
                        variant="secondary"
                        className="text-xs max-w-full break-words"
                      >
                        {tag}
                      </Badge>
                    ))}
                  </div>
                ) : prop.type === 'link' ? (
                  <span
                    className="text-sm text-foreground cursor-pointer hover:text-primary break-all block w-full max-w-full overflow-hidden"
                    style={{
                      wordBreak: 'break-all',
                      overflowWrap: 'anywhere',
                      maxWidth: '100%',
                      width: '100%',
                      display: 'block'
                    }}
                    title={prop.fullValue}
                    onClick={(e) => {
                      e.stopPropagation()
                      window.open(prop.fullValue, '_blank')
                    }}
                  >
                    {prop.value}
                  </span>
                ) : (
                  <span
                    className="text-sm text-foreground break-words block w-full max-w-full overflow-hidden"
                    style={{
                      wordBreak: 'break-word',
                      overflowWrap: 'break-word',
                      maxWidth: '100%',
                      width: '100%',
                      display: 'block'
                    }}
                  >
                    {prop.value}
                  </span>
                )}
              </div>
            </div>
          ))}

          {/* 已读状态 */}
          <div className="flex items-start gap-3 py-1 min-w-0">
            <BookOpen className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
            <span className="text-sm text-muted-foreground min-w-[60px] flex-shrink-0">
              已读
            </span>
            <div className="flex-1 w-full max-w-full min-w-0 overflow-hidden">
              <Checkbox
                checked={isRead}
                onCheckedChange={onIsReadChange}
                className="h-4 w-4"
              />
            </div>
          </div>

          {/* 分类 */}
          <div className="flex items-start gap-3 py-1 min-w-0">
            <FolderOpen className="h-4 w-4 text-muted-foreground flex-shrink-0 mt-0.5" />
            <span className="text-sm text-muted-foreground min-w-[60px] flex-shrink-0">
              Category
            </span>
            <div className="flex-1 w-full max-w-full min-w-0 overflow-hidden">
              <span className="text-sm text-foreground break-words block w-full max-w-full overflow-hidden">
                {category}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PropertiesSection
