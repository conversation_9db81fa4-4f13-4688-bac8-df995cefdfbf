import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  MousePointer, 
  X, 
  Info
} from 'lucide-react'

interface SelectionModeIndicatorProps {
  onExitSelection: () => void
}

const SelectionModeIndicator: React.FC<SelectionModeIndicatorProps> = ({
  onExitSelection
}) => {
  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardContent className="p-3">
        <div className="flex items-center gap-3">
          <div className="p-1.5 rounded-full bg-amber-100">
            <MousePointer className="h-4 w-4 text-amber-600" />
          </div>
          
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <span className="text-sm font-medium">选择模式进行中</span>
              <Badge variant="outline" className="text-xs border-amber-300 text-amber-700">
                活动中
              </Badge>
            </div>
            <p className="text-xs text-amber-700">
              在页面上点击或框选要剪藏的区域，按 ESC 键退出选择模式
            </p>
          </div>
          
          <Button
            onClick={onExitSelection}
            variant="outline"
            size="sm"
            className="border-amber-300 text-amber-700 hover:bg-amber-100"
          >
            <X className="h-3 w-3 mr-1" />
            退出
          </Button>
        </div>
        
        <div className="mt-2 p-2 bg-amber-100 rounded text-xs text-amber-800 flex items-start gap-2">
          <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
          <div>
            <strong>使用提示：</strong>
            在当前页面中点击单个元素或拖拽框选多个元素，选择完成后内容将自动显示在剪藏预览中。
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default SelectionModeIndicator
