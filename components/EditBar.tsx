import React, { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { 
  Edit, 
  Check, 
  X, 
  Tags, 
  Image,
  Plus
} from 'lucide-react'

interface EditBarProps {
  title: string
  tags: string[]
  coverImage?: string
  onTitleChange: (title: string) => void
  onTagsChange: (tags: string[]) => void
  onCoverChange: (cover?: string) => void
  onSelectCover: () => void
}

const EditBar: React.FC<EditBarProps> = ({
  title,
  tags,
  coverImage,
  onTitleChange,
  onTagsChange,
  onCoverChange,
  onSelectCover
}) => {
  const [editingTitle, setEditingTitle] = useState(false)
  const [editingTags, setEditingTags] = useState(false)
  const [tempTitle, setTempTitle] = useState(title)
  const [tempTagInput, setTempTagInput] = useState('')

  const handleTitleSave = () => {
    onTitleChange(tempTitle)
    setEditingTitle(false)
  }

  const handleTitleCancel = () => {
    setTempTitle(title)
    setEditingTitle(false)
  }

  const handleAddTag = () => {
    if (tempTagInput.trim() && !tags.includes(tempTagInput.trim())) {
      const newTags = [...tags, tempTagInput.trim()]
      onTagsChange(newTags)
      setTempTagInput('')
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove)
    onTagsChange(newTags)
  }

  const handleTagInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleAddTag()
    } else if (e.key === 'Escape') {
      setEditingTags(false)
      setTempTagInput('')
    }
  }

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4 space-y-3">
        {/* 标题编辑 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-muted-foreground">标题</label>
            {!editingTitle && (
              <Button
                onClick={() => setEditingTitle(true)}
                variant="ghost"
                size="sm"
                className="h-6 px-2"
              >
                <Edit className="h-3 w-3" />
              </Button>
            )}
          </div>
          
          {editingTitle ? (
            <div className="flex gap-2">
              <Input
                value={tempTitle}
                onChange={(e) => setTempTitle(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleTitleSave()
                  if (e.key === 'Escape') handleTitleCancel()
                }}
                className="text-sm"
                autoFocus
              />
              <Button onClick={handleTitleSave} size="sm" variant="outline">
                <Check className="h-3 w-3" />
              </Button>
              <Button onClick={handleTitleCancel} size="sm" variant="outline">
                <X className="h-3 w-3" />
              </Button>
            </div>
          ) : (
            <p className="text-sm text-foreground line-clamp-2 cursor-pointer hover:text-primary"
               onClick={() => setEditingTitle(true)}>
              {title || '点击编辑标题...'}
            </p>
          )}
        </div>

        {/* 标签编辑 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-muted-foreground">标签</label>
            <Button
              onClick={() => setEditingTags(!editingTags)}
              variant="ghost"
              size="sm"
              className="h-6 px-2"
            >
              <Tags className="h-3 w-3" />
            </Button>
          </div>
          
          <div className="flex flex-wrap gap-1">
            {tags.map((tag, index) => (
              <Badge 
                key={index} 
                variant="secondary" 
                className="text-xs cursor-pointer hover:bg-red-100"
                onClick={() => handleRemoveTag(tag)}
              >
                {tag}
                <X className="h-2 w-2 ml-1" />
              </Badge>
            ))}
            
            {editingTags && (
              <div className="flex gap-1 mt-1 w-full">
                <Input
                  value={tempTagInput}
                  onChange={(e) => setTempTagInput(e.target.value)}
                  onKeyDown={handleTagInputKeyDown}
                  placeholder="输入标签..."
                  className="text-xs h-6 flex-1"
                  autoFocus
                />
                <Button onClick={handleAddTag} size="sm" variant="outline" className="h-6 px-2">
                  <Plus className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
          
          {tags.length === 0 && !editingTags && (
            <p className="text-xs text-muted-foreground cursor-pointer hover:text-primary"
               onClick={() => setEditingTags(true)}>
              点击添加标签...
            </p>
          )}
        </div>

        {/* 封面编辑 */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <label className="text-sm font-medium text-muted-foreground">封面图片</label>
            <Button
              onClick={onSelectCover}
              variant="ghost"
              size="sm"
              className="h-6 px-2"
            >
              <Image className="h-3 w-3" />
            </Button>
          </div>
          
          {coverImage ? (
            <div className="relative group">
              <img
                src={coverImage}
                alt="封面"
                className="w-full h-20 object-cover rounded border cursor-pointer hover:opacity-80"
                onClick={onSelectCover}
              />
              <Button
                onClick={() => onCoverChange(undefined)}
                variant="outline"
                size="sm"
                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          ) : (
            <div
              onClick={onSelectCover}
              className="w-full h-16 border-2 border-dashed border-muted-foreground/25 rounded flex items-center justify-center cursor-pointer hover:border-primary/50 transition-colors"
            >
              <div className="text-center">
                <Image className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                <p className="text-xs text-muted-foreground">点击选择封面</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default EditBar
