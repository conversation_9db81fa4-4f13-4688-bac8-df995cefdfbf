{"name": "mpclipper", "description": "A modern browser extension template with sidepanel support, built with WXT + Tailwind CSS 4.0 + shadcn/ui", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "wxt", "dev:chrome": "wxt -b chrome", "dev:firefox": "wxt -b firefox", "dev:edge": "wxt -b edge", "dev:safari": "wxt -b safari", "build": "wxt build", "build:chrome": "wxt build -b chrome", "build:firefox": "wxt build -b firefox", "build:edge": "wxt build -b edge", "build:safari": "wxt build -b safari", "zip": "wxt zip", "zip:chrome": "wxt zip -b chrome", "zip:firefox": "wxt zip -b firefox", "zip:edge": "wxt zip -b edge", "zip:safari": "wxt zip -b safari", "compile": "tsc --noEmit", "postinstall": "wxt prepare"}, "dependencies": {"@aws-sdk/client-s3": "^3.844.0", "@langchain/anthropic": "^0.3.26", "@langchain/core": "^0.3.72", "@langchain/openai": "^0.6.9", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@types/file-saver": "^2.0.7", "@types/turndown": "^5.0.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "langchain": "^0.3.31", "lucide-react": "^0.525.0", "p-limit": "^7.1.1", "p-retry": "^7.0.0", "radix-ui": "^1.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "turndown": "^7.2.0", "tw-animate-css": "^1.3.4"}, "devDependencies": {"@types/node": "^24.0.7", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.3", "@wxt-dev/module-react": "^1.1.3", "typescript": "^5.8.3", "wxt": "^0.20.6"}, "packageManager": "pnpm@9.10.0"}