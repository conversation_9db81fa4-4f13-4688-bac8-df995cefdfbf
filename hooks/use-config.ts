import { storage } from '#imports'
import { useEffect, useState } from 'react'

export interface AIConfig {
  provider: 'openai' | 'claude' | 'deepseek' | 'kimi' | 'openrouter' | 'custom'
  apiKey: string
  baseUrl?: string
  model: string
  temperature: number
  maxTokens: number
  enabled: boolean
}

export interface ImageConfig {
  provider: 'alioss' | 'qiniu' | 's3'
  accessKey?: string
  secretKey?: string
  bucket?: string
  region?: string
  endpoint?: string
  domain?: string
  path?: string
  forcePathStyle?: boolean // 强制使用path-style URL（适用于自建S3兼容服务）
  enabled: boolean
}

export interface NotionConfig {
  token: string
  databaseId: string
  enabled: boolean
}

export interface LocalSaveConfig {
  enabled: boolean
  saveDirectory?: string // 保存目录路径
}

export interface ExportConfig {
  notion: NotionConfig
  localSave: LocalSaveConfig
}

export interface RulesConfig {
  removeEmojiImages: boolean // 移除表情符号图片
}

export interface UserConfig {
  ai: AIConfig
  image: ImageConfig
  export: ExportConfig
  rules: RulesConfig
}

const defaultConfig: UserConfig = {
  ai: {
    provider: 'openai',
    apiKey: '',
    model: 'gpt-3.5-turbo',
    temperature: 0.7,
    maxTokens: 2000,
    enabled: false
  },
  image: {
    provider: 'alioss',
    enabled: false
  },
  export: {
    notion: {
      token: '',
      databaseId: '',
      enabled: false
    },
    localSave: {
      enabled: true, // 默认启用本地保存
      saveDirectory: '' // 空字符串表示使用浏览器默认下载目录
    }
  },
  rules: {
    removeEmojiImages: true // 默认启用表情符号移除
  }
}

// Define storage item
const userConfig = storage.defineItem<UserConfig>('local:user-config', {
  fallback: defaultConfig
})

export function useConfig() {
  const [config, setConfig] = useState<UserConfig>(defaultConfig)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    try {
      setLoading(true)
      const stored = await userConfig.getValue()
      setConfig(stored)
    } catch (err) {
      setError('Failed to load config')
      console.error('Failed to load config:', err)
    } finally {
      setLoading(false)
    }
  }

  const saveConfig = async (newConfig: UserConfig) => {
    try {
      await userConfig.setValue(newConfig)
      setConfig(newConfig)
      setError(null)
    } catch (err) {
      setError('Failed to save config')
      console.error('Failed to save config:', err)
    }
  }

  const updateAI = (aiConfig: Partial<AIConfig>) => {
    const newConfig = {
      ...config,
      ai: { ...config.ai, ...aiConfig }
    }
    saveConfig(newConfig)
  }

  const updateImage = (imageConfig: Partial<ImageConfig>) => {
    const newConfig = {
      ...config,
      image: { ...config.image, ...imageConfig }
    }
    saveConfig(newConfig)
  }

  const updateNotion = (notionConfig: Partial<NotionConfig>) => {
    const newConfig = {
      ...config,
      export: { 
        ...config.export, 
        notion: { ...config.export.notion, ...notionConfig } 
      }
    }
    saveConfig(newConfig)
  }

  const updateLocalSave = (localSaveConfig: Partial<LocalSaveConfig>) => {
    const newConfig = {
      ...config,
      export: { 
        ...config.export, 
        localSave: { ...config.export.localSave, ...localSaveConfig } 
      }
    }
    saveConfig(newConfig)
  }

  const updateExport = (exportConfig: Partial<ExportConfig>) => {
    const newConfig = {
      ...config,
      export: { ...config.export, ...exportConfig }
    }
    saveConfig(newConfig)
  }

  const updateRules = (rulesConfig: Partial<RulesConfig>) => {
    const newConfig = {
      ...config,
      rules: { ...config.rules, ...rulesConfig }
    }
    saveConfig(newConfig)
  }

  const resetConfig = () => {
    saveConfig(defaultConfig)
  }

  const exportConfig = () => {
    const dataStr = JSON.stringify(config, null, 2)
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr)
    const exportFileDefaultName = 'web-clipper-config.json'
    
    const linkElement = document.createElement('a')
    linkElement.setAttribute('href', dataUri)
    linkElement.setAttribute('download', exportFileDefaultName)
    linkElement.click()
  }

  const importConfig = (configData: UserConfig) => {
    try {
      const mergedConfig = { ...defaultConfig, ...configData }
      saveConfig(mergedConfig)
    } catch (err) {
      setError('Failed to import config')
      console.error('Failed to import config:', err)
    }
  }

  const validateConfig = () => {
    const issues: string[] = []

    if (config.ai.enabled && !config.ai.apiKey) {
      issues.push('AI API Key is required when AI is enabled')
    }

    if (config.image.enabled) {
      if (!config.image.accessKey || !config.image.secretKey) {
        issues.push('图床服务需要 Access Key 和 Secret Key')
      }
      if (!config.image.bucket) {
        issues.push('图床服务需要 Bucket 名称')
      }
      if (!config.image.region) {
        issues.push('图床服务需要区域信息')
      }
    }

    if (config.export.notion.enabled && (!config.export.notion.token || !config.export.notion.databaseId)) {
      issues.push('Notion token and database ID are required')
    }

    return issues
  }

  return {
    config,
    loading,
    error,
    updateAI,
    updateImage,
    updateNotion,
    updateLocalSave,
    updateExport,
    updateRules,
    resetConfig,
    exportConfig,
    importConfig,
    validateConfig,
    saveConfig
  }
}