import { useState, useEffect } from 'react'

// 剪藏数据类型
export interface ClipData {
  title: string
  site: string
  url: string
  timestamp: string
  content: string
  contentHTML?: string
  favicon?: string
  coverImage?: string | null
  author?: string | null
  publishDate?: string | null
  tags?: string
  aiAnalysis?: {
    selector: string
    reasoning: string
    confidence: number
  }
  selectedElements?: Array<{
    index: number
    html: string
    markdown: string
    text: string
    tagName: string
    className: string
  }>
}

// 存储最近的剪藏记录（最多保存10条）
const CLIP_HISTORY_KEY = 'clip-history'

// 浏览器存储工具函数
const getClipHistory = async (): Promise<ClipData[]> => {
  try {
    const result = await browser.storage.local.get(CLIP_HISTORY_KEY)
    return result[CLIP_HISTORY_KEY] || []
  } catch (error) {
    console.error('Failed to get clip history:', error)
    return []
  }
}

const setClipHistory = async (history: ClipData[]): Promise<void> => {
  try {
    await browser.storage.local.set({ [CLIP_HISTORY_KEY]: history })
  } catch (error) {
    console.error('Failed to set clip history:', error)
  }
}

export function useClipHistory() {
  const [history, setHistory] = useState<ClipData[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPageInfo, setCurrentPageInfo] = useState<{
    url: string | null
    canClip: boolean
    title: string | null
  }>({
    url: null,
    canClip: false,
    title: null
  })

  useEffect(() => {
    loadHistory()
    checkCurrentPage()
  }, [])

  const loadHistory = async () => {
    try {
      setLoading(true)
      const stored = await getClipHistory()
      setHistory(stored)
    } catch (err) {
      console.error('Failed to load clip history:', err)
    } finally {
      setLoading(false)
    }
  }

  // 检查当前页面是否可剪藏
  const checkCurrentPage = async () => {
    try {
      const [activeTab] = await browser.tabs.query({ 
        active: true, 
        currentWindow: true 
      })

      if (!activeTab || !activeTab.id || !activeTab.url) {
        setCurrentPageInfo({
          url: null,
          canClip: false,
          title: null
        })
        return
      }

      // 检查是否是不支持的页面类型
      const unsupportedPatterns = [
        'chrome://',
        'chrome-extension://',
        'edge://',
        'about:',
        'moz-extension://',
        'file://'
      ]

      const canClip = !unsupportedPatterns.some(pattern => 
        activeTab.url!.startsWith(pattern)
      )

      setCurrentPageInfo({
        url: activeTab.url,
        canClip,
        title: activeTab.title || null
      })
    } catch (err) {
      console.error('Failed to check current page:', err)
      setCurrentPageInfo({
        url: null,
        canClip: false,
        title: null
      })
    }
  }

  // 添加新的剪藏记录
  const addClipToHistory = async (clipData: ClipData) => {
    try {
      const newHistory = [clipData, ...history].slice(0, 10) // 只保存最近10条
      await setClipHistory(newHistory)
      setHistory(newHistory)
    } catch (err) {
      console.error('Failed to save clip to history:', err)
    }
  }

  // 获取最新的剪藏记录
  const getLatestClip = (): ClipData | null => {
    return history.length > 0 ? history[0] : null
  }

  // 清除历史记录
  const clearHistory = async () => {
    try {
      await setClipHistory([])
      setHistory([])
    } catch (err) {
      console.error('Failed to clear history:', err)
    }
  }

  // 删除特定的剪藏记录
  const removeClipFromHistory = async (timestamp: string) => {
    try {
      const newHistory = history.filter(clip => clip.timestamp !== timestamp)
      await setClipHistory(newHistory)
      setHistory(newHistory)
    } catch (err) {
      console.error('Failed to remove clip from history:', err)
    }
  }

  // 判断应该显示什么页面
  const getPageToShow = (): 'current' | 'latest' | 'welcome' => {
    if (currentPageInfo.canClip) {
      return 'current'
    }
    if (history.length > 0) {
      return 'latest'
    }
    return 'welcome'
  }

  return {
    history,
    loading,
    currentPageInfo,
    addClipToHistory,
    getLatestClip,
    clearHistory,
    removeClipFromHistory,
    getPageToShow,
    checkCurrentPage
  }
}
