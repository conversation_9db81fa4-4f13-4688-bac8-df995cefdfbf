以下是高优先级、可落地的设计与实现问题清单（含改进建议）：

### 功能设计/产品层面
- 权限过宽
  - 问题：`wxt.config.ts` 中 `host_permissions` 为 `<all_urls>`。
  - 建议：改为最小权限 + 运行时按需申请（或站点白名单）。
- 第三方配置与能力展示不匹配
  - 问题：UI 支持阿里云/七牛，但实现中直接报错（需 S3 兼容/后端代理）。
  - 建议：未实现前从 UI 隐藏或“置灰并提示”；优先支持预签名 URL 流程。
- Notion 属性映射僵硬
  - 问题：写死 `Title/URL/Author/Published/Source/Clipped/Tags/Cover`。
  - 建议：设置页新增属性映射，适配不同数据库结构。

### 安全/合规
- 密钥明文存储与泄露风险
  - 问题：`local` 存储保存 AI Key、Notion Token、S3 密钥，且日志大量输出配置信息。
  - 建议：统一日志降噪与脱敏；引入加密存储（用户口令派生密钥）或后端换取临时凭证；默认禁用详细日志。
- 不安全的内容渲染
  - 问题：侧边栏用 `react-markdown + rehype-raw`，未做 HTML 消毒。
  - 建议：引入 `rehype-sanitize` 或 DOMPurify，在展示前严格净化。
- 第三方代理与 Referer 处理
  - 问题：内容脚本含 `cors-anywhere.herokuapp.com` 兜底，background 尝试自设 `Referer`（可能被浏览器忽略）。
  - 建议：删除公共代理；如需代理，自建可控服务；用 declarativeNetRequest 或专用代理替代 Referer Hack。

### 架构/实现质量
- 内容脚本体积与职责过大
  - 问题：`entrypoints/content.ts` 过于臃肿（站点适配/下载/预加载/抽取混杂）。
  - 建议：模块化拆分（抽取规则、图片下载、AI 辅助、站点适配、公共工具），引入单测。
- Notion 客户端重复实现
  - 问题：`lib/notion-service.ts` 与 `lib/notion-api-client.ts` 并存（前者未用）。
  - 建议：仅保留 `notion-api-client`（background 代理），删除冗余文件与依赖（`@notionhq/client`）。
- AI 解析健壮性不足
  - 问题：AI 响应用正则抓第一段 JSON（`/\{[\s\S]*\}/`）易碎；未做重试/降级策略。
  - 建议：限定模型输出（JSON Schema/工具调用），增加严格解析与重试/回退。
- html2canvas 重复与首屏体积
  - 问题：`lib/image-generator.ts` 静态引入 html2canvas，`ClipPage.tsx` 也动态引入，增加首包。
  - 建议：统一改为按需动态导入，避免首屏加载。
- Base URL 配置易错
  - 问题：设置页引导 `api.deepseek.com` 之类裸域；`ai-content-extractor` 直接拼接，易 404。
  - 建议：校验并规范化成完整 URL（含协议与路径）。
- 规则与清洗的副作用
  - 问题：Emoji/符号清洗正则覆盖面过大（含 `\u{2000}-\u{206F}`），易误删标点。
  - 建议：缩小匹配范围，按图片表情与特定资源来源来剔除。
- 图片下载与缓存
  - 问题：预加载缓存 Map 仅按时间清理，可能占用内存；多种下载路径复杂且无指标监控。
  - 建议：缓存加容量/尺寸上限与淘汰策略；统一下载策略并加埋点监控。
- Provider 默认与回退
  - 问题：`ImageUploadManager.getDefaultProvider()` 可能回落到未实现的 `alioss`。
  - 建议：严格校验 provider，不可用则显式报错或回退到 `s3`（前置校验 UI 提示）。
- `app.config.ts` 逻辑错误
  - 问题：`enableChat: import.meta.env.WXT_ENABLE_CHAT === 'true' || true` 永远为 true。
  - 建议：去掉 `|| true`，尊重环境变量。

### 体验/可维护性
- 日志噪音
  - 问题：大量 `console.log`，含配置与流程细节。
  - 建议：加 `LOG_LEVEL` 与调试开关，默认生产最少日志。
- i18n 与可用性
  - 问题：中文界面固定；若对外发布需多语言。
  - 建议：引入 i18n（如 i18next），按浏览器语言切换。
- 依赖与包体
  - 问题：`@aws-sdk/client-s3` 体积较大。
  - 建议：改预签名 URL 直传，或拆分只引入所需模块；优化打包分块。

### 建议的近期迭代优先级
1) 安全与合规：HTML 消毒、日志脱敏、权限收敛、删除公共代理。  
2) 架构瘦身：拆分 `content.ts`、删除未用 Notion 客户端与 Hook、统一 html2canvas 动态引入。  
3) 体验与可用性：隐藏未实现图床、修复 `enableChat` 逻辑、优化 Base URL 校验与错误提示。  
4) 可扩展性：Notion 属性映射、下载/上传链路埋点监控和重试策略、AI 输出解析加固。

- 若需要，我可以直接提交一轮“瘦身与安全加固”的 edits（删除冗余代码、修复 `enableChat`、加 `rehype-sanitize`、权限最小化草案），再跑一次构建验收。